[{"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/add-template/page.tsx": "1", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/custom-requests/page.tsx": "2", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/page.tsx": "3", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/setup/page.tsx": "4", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/auth/page.tsx": "5", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/categories/page.tsx": "6", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/contact/page.tsx": "7", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/custom-request/page.tsx": "8", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/dashboard/page.tsx": "9", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx": "10", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/orders/page.tsx": "11", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/page.tsx": "12", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/profile/page.tsx": "13", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/success/page.tsx": "14", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/templates/page.tsx": "15", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/CategoriesSection.tsx": "16", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/FeaturedTemplates.tsx": "17", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Footer.tsx": "18", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/HeroSection.tsx": "19", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Navbar.tsx": "20", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/StatsSection.tsx": "21", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ThemeToggle.tsx": "22", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/EnhancedTemplateDialog.tsx": "23", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/TemplateDialog.tsx": "24", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/TemplatesTab.tsx": "25", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/alert.tsx": "26", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/avatar.tsx": "27", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx": "28", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/button.tsx": "29", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx": "30", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dialog.tsx": "31", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dropdown-menu.tsx": "32", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/form.tsx": "33", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/input.tsx": "34", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx": "35", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/navigation-menu.tsx": "36", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx": "37", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/sheet.tsx": "38", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/switch.tsx": "39", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/table.tsx": "40", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/tabs.tsx": "41", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx": "42", "/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx": "43", "/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/ThemeContext.tsx": "44", "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebase.ts": "45", "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts": "46", "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/sampleData.ts": "47", "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/toast.ts": "48", "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/utils.ts": "49", "/home/<USER>/Documents/augment-projects/kaleidonex/src/types/index.ts": "50"}, {"size": 7606, "mtime": 1749555387896, "results": "51", "hashOfConfig": "52"}, {"size": 13580, "mtime": 1749553260086, "results": "53", "hashOfConfig": "52"}, {"size": 22798, "mtime": 1749557092157, "results": "54", "hashOfConfig": "52"}, {"size": 6602, "mtime": 1749555045186, "results": "55", "hashOfConfig": "52"}, {"size": 10665, "mtime": 1749554621673, "results": "56", "hashOfConfig": "52"}, {"size": 9348, "mtime": 1749538858315, "results": "57", "hashOfConfig": "52"}, {"size": 11150, "mtime": 1749538796734, "results": "58", "hashOfConfig": "52"}, {"size": 13070, "mtime": 1749553702399, "results": "59", "hashOfConfig": "52"}, {"size": 8726, "mtime": 1749553727399, "results": "60", "hashOfConfig": "52"}, {"size": 1243, "mtime": 1749557454040, "results": "61", "hashOfConfig": "52"}, {"size": 10380, "mtime": 1749538900143, "results": "62", "hashOfConfig": "52"}, {"size": 450, "mtime": 1749531355825, "results": "63", "hashOfConfig": "52"}, {"size": 10355, "mtime": 1749556787391, "results": "64", "hashOfConfig": "52"}, {"size": 6719, "mtime": 1749554180543, "results": "65", "hashOfConfig": "52"}, {"size": 20411, "mtime": 1749557150709, "results": "66", "hashOfConfig": "52"}, {"size": 3508, "mtime": 1749539431946, "results": "67", "hashOfConfig": "52"}, {"size": 14387, "mtime": 1749557389446, "results": "68", "hashOfConfig": "52"}, {"size": 6226, "mtime": 1749556407156, "results": "69", "hashOfConfig": "52"}, {"size": 2398, "mtime": 1749553762652, "results": "70", "hashOfConfig": "52"}, {"size": 8599, "mtime": 1749557481942, "results": "71", "hashOfConfig": "52"}, {"size": 2171, "mtime": 1749553782678, "results": "72", "hashOfConfig": "52"}, {"size": 569, "mtime": 1749557420006, "results": "73", "hashOfConfig": "52"}, {"size": 22566, "mtime": 1749556225163, "results": "74", "hashOfConfig": "52"}, {"size": 10813, "mtime": 1749556064583, "results": "75", "hashOfConfig": "52"}, {"size": 12366, "mtime": 1749556275710, "results": "76", "hashOfConfig": "52"}, {"size": 1614, "mtime": 1749531721138, "results": "77", "hashOfConfig": "52"}, {"size": 1097, "mtime": 1749531171148, "results": "78", "hashOfConfig": "52"}, {"size": 1631, "mtime": 1749531171120, "results": "79", "hashOfConfig": "52"}, {"size": 2123, "mtime": 1749531170878, "results": "80", "hashOfConfig": "52"}, {"size": 1989, "mtime": 1749531170936, "results": "81", "hashOfConfig": "52"}, {"size": 3982, "mtime": 1749531171060, "results": "82", "hashOfConfig": "52"}, {"size": 8284, "mtime": 1749531171212, "results": "83", "hashOfConfig": "52"}, {"size": 3759, "mtime": 1749531171026, "results": "84", "hashOfConfig": "52"}, {"size": 967, "mtime": 1749531170946, "results": "85", "hashOfConfig": "52"}, {"size": 611, "mtime": 1749531170959, "results": "86", "hashOfConfig": "52"}, {"size": 6664, "mtime": 1749531171239, "results": "87", "hashOfConfig": "52"}, {"size": 6253, "mtime": 1749531171312, "results": "88", "hashOfConfig": "52"}, {"size": 4090, "mtime": 1749531171267, "results": "89", "hashOfConfig": "52"}, {"size": 1153, "mtime": 1749555743647, "results": "90", "hashOfConfig": "52"}, {"size": 2448, "mtime": 1749531171281, "results": "91", "hashOfConfig": "52"}, {"size": 1969, "mtime": 1749531171290, "results": "92", "hashOfConfig": "52"}, {"size": 759, "mtime": 1749531171318, "results": "93", "hashOfConfig": "52"}, {"size": 4343, "mtime": 1749540356571, "results": "94", "hashOfConfig": "52"}, {"size": 1563, "mtime": 1749557406435, "results": "95", "hashOfConfig": "52"}, {"size": 685, "mtime": 1749538562380, "results": "96", "hashOfConfig": "52"}, {"size": 5740, "mtime": 1749557270777, "results": "97", "hashOfConfig": "52"}, {"size": 10550, "mtime": 1749540821188, "results": "98", "hashOfConfig": "52"}, {"size": 251, "mtime": 1749555896349, "results": "99", "hashOfConfig": "52"}, {"size": 166, "mtime": 1749531044432, "results": "100", "hashOfConfig": "52"}, {"size": 1918, "mtime": 1749556866041, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "9re6db", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/add-template/page.tsx", ["252"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/custom-requests/page.tsx", ["253", "254", "255"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/page.tsx", ["256", "257", "258", "259", "260", "261", "262", "263", "264"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/setup/page.tsx", ["265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/auth/page.tsx", ["277", "278"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/categories/page.tsx", ["279", "280"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/contact/page.tsx", ["281", "282", "283", "284", "285"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/custom-request/page.tsx", ["286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/dashboard/page.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/orders/page.tsx", ["298", "299"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/page.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/profile/page.tsx", ["300", "301"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/success/page.tsx", ["302"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/templates/page.tsx", ["303", "304"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/CategoriesSection.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/FeaturedTemplates.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Footer.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/HeroSection.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Navbar.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/StatsSection.tsx", ["305", "306", "307", "308", "309", "310"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ThemeToggle.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/EnhancedTemplateDialog.tsx", ["311"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/TemplateDialog.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/TemplatesTab.tsx", ["312"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/alert.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/avatar.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/button.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dialog.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dropdown-menu.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/form.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/input.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/navigation-menu.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/sheet.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/switch.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/table.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/tabs.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx", ["313", "314", "315"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/ThemeContext.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebase.ts", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts", ["316", "317", "318", "319", "320", "321"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/sampleData.ts", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/toast.ts", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/utils.ts", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/types/index.ts", [], [], {"ruleId": "322", "severity": 2, "message": "323", "line": 11, "column": 27, "nodeType": null, "messageId": "324", "endLine": 11, "endColumn": 33}, {"ruleId": "322", "severity": 2, "message": "325", "line": 22, "column": 3, "nodeType": null, "messageId": "324", "endLine": 22, "endColumn": 14}, {"ruleId": "326", "severity": 2, "message": "327", "line": 43, "column": 23, "nodeType": "328", "messageId": "329", "endLine": 43, "endColumn": 26, "suggestions": "330"}, {"ruleId": "326", "severity": 2, "message": "327", "line": 70, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 70, "endColumn": 24, "suggestions": "331"}, {"ruleId": "322", "severity": 2, "message": "332", "line": 11, "column": 3, "nodeType": null, "messageId": "324", "endLine": 11, "endColumn": 15}, {"ruleId": "322", "severity": 2, "message": "333", "line": 15, "column": 3, "nodeType": null, "messageId": "324", "endLine": 15, "endColumn": 11}, {"ruleId": "322", "severity": 2, "message": "334", "line": 16, "column": 3, "nodeType": null, "messageId": "324", "endLine": 16, "endColumn": 12}, {"ruleId": "322", "severity": 2, "message": "335", "line": 56, "column": 10, "nodeType": null, "messageId": "324", "endLine": 56, "endColumn": 24}, {"ruleId": "322", "severity": 2, "message": "336", "line": 58, "column": 10, "nodeType": null, "messageId": "324", "endLine": 58, "endColumn": 21}, {"ruleId": "326", "severity": 2, "message": "327", "line": 84, "column": 23, "nodeType": "328", "messageId": "329", "endLine": 84, "endColumn": 26, "suggestions": "337"}, {"ruleId": "322", "severity": 2, "message": "338", "line": 111, "column": 9, "nodeType": null, "messageId": "324", "endLine": 111, "endColumn": 34}, {"ruleId": "326", "severity": 2, "message": "327", "line": 115, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 115, "endColumn": 24, "suggestions": "339"}, {"ruleId": "326", "severity": 2, "message": "327", "line": 125, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 125, "endColumn": 24, "suggestions": "340"}, {"ruleId": "322", "severity": 2, "message": "341", "line": 7, "column": 10, "nodeType": null, "messageId": "324", "endLine": 7, "endColumn": 15}, {"ruleId": "322", "severity": 2, "message": "342", "line": 8, "column": 10, "nodeType": null, "messageId": "324", "endLine": 8, "endColumn": 15}, {"ruleId": "322", "severity": 2, "message": "343", "line": 9, "column": 49, "nodeType": null, "messageId": "324", "endLine": 9, "endColumn": 55}, {"ruleId": "322", "severity": 2, "message": "344", "line": 17, "column": 10, "nodeType": null, "messageId": "324", "endLine": 17, "endColumn": 22}, {"ruleId": "322", "severity": 2, "message": "345", "line": 18, "column": 10, "nodeType": null, "messageId": "324", "endLine": 18, "endColumn": 22}, {"ruleId": "322", "severity": 2, "message": "346", "line": 19, "column": 10, "nodeType": null, "messageId": "324", "endLine": 19, "endColumn": 20}, {"ruleId": "322", "severity": 2, "message": "347", "line": 20, "column": 21, "nodeType": null, "messageId": "324", "endLine": 20, "endColumn": 33}, {"ruleId": "326", "severity": 2, "message": "327", "line": 32, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 32, "endColumn": 24, "suggestions": "348"}, {"ruleId": "322", "severity": 2, "message": "349", "line": 39, "column": 9, "nodeType": null, "messageId": "324", "endLine": 39, "endColumn": 24}, {"ruleId": "326", "severity": 2, "message": "327", "line": 57, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 57, "endColumn": 24, "suggestions": "350"}, {"ruleId": "351", "severity": 2, "message": "352", "line": 138, "column": 110, "nodeType": "353", "messageId": "354", "suggestions": "355"}, {"ruleId": "351", "severity": 2, "message": "352", "line": 138, "column": 116, "nodeType": "353", "messageId": "354", "suggestions": "356"}, {"ruleId": "326", "severity": 2, "message": "327", "line": 60, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 60, "endColumn": 24, "suggestions": "357"}, {"ruleId": "326", "severity": 2, "message": "327", "line": 99, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 99, "endColumn": 24, "suggestions": "358"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 230, "column": 16, "nodeType": "353", "messageId": "354", "suggestions": "360"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 230, "column": 32, "nodeType": "353", "messageId": "354", "suggestions": "361"}, {"ruleId": "322", "severity": 2, "message": "362", "line": 19, "column": 3, "nodeType": null, "messageId": "324", "endLine": 19, "endColumn": 6}, {"ruleId": "326", "severity": 2, "message": "327", "line": 102, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 102, "endColumn": 24, "suggestions": "363"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 117, "column": 46, "nodeType": "353", "messageId": "354", "suggestions": "364"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 139, "column": 80, "nodeType": "353", "messageId": "354", "suggestions": "365"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 150, "column": 47, "nodeType": "353", "messageId": "354", "suggestions": "366"}, {"ruleId": "322", "severity": 2, "message": "367", "line": 16, "column": 3, "nodeType": null, "messageId": "324", "endLine": 16, "endColumn": 8}, {"ruleId": "322", "severity": 2, "message": "368", "line": 17, "column": 3, "nodeType": null, "messageId": "324", "endLine": 17, "endColumn": 13}, {"ruleId": "322", "severity": 2, "message": "369", "line": 18, "column": 3, "nodeType": null, "messageId": "324", "endLine": 18, "endColumn": 11}, {"ruleId": "322", "severity": 2, "message": "370", "line": 19, "column": 3, "nodeType": null, "messageId": "324", "endLine": 19, "endColumn": 12}, {"ruleId": "322", "severity": 2, "message": "362", "line": 21, "column": 3, "nodeType": null, "messageId": "324", "endLine": 21, "endColumn": 6}, {"ruleId": "322", "severity": 2, "message": "371", "line": 24, "column": 3, "nodeType": null, "messageId": "324", "endLine": 24, "endColumn": 8}, {"ruleId": "322", "severity": 2, "message": "372", "line": 65, "column": 17, "nodeType": null, "messageId": "324", "endLine": 65, "endColumn": 25}, {"ruleId": "326", "severity": 2, "message": "327", "line": 120, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 120, "endColumn": 24, "suggestions": "373"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 174, "column": 14, "nodeType": "353", "messageId": "354", "suggestions": "374"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 174, "column": 30, "nodeType": "353", "messageId": "354", "suggestions": "375"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 185, "column": 63, "nodeType": "353", "messageId": "354", "suggestions": "376"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 337, "column": 58, "nodeType": "353", "messageId": "354", "suggestions": "377"}, {"ruleId": "378", "severity": 1, "message": "379", "line": 202, "column": 25, "nodeType": "380", "endLine": 206, "endColumn": 27}, {"ruleId": "351", "severity": 2, "message": "359", "line": 279, "column": 96, "nodeType": "353", "messageId": "354", "suggestions": "381"}, {"ruleId": "322", "severity": 2, "message": "382", "line": 11, "column": 10, "nodeType": null, "messageId": "324", "endLine": 11, "endColumn": 15}, {"ruleId": "326", "severity": 2, "message": "327", "line": 107, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 107, "endColumn": 24, "suggestions": "383"}, {"ruleId": "351", "severity": 2, "message": "359", "line": 76, "column": 61, "nodeType": "353", "messageId": "354", "suggestions": "384"}, {"ruleId": "385", "severity": 1, "message": "386", "line": 49, "column": 6, "nodeType": "387", "endLine": 49, "endColumn": 55, "suggestions": "388"}, {"ruleId": "378", "severity": 1, "message": "379", "line": 342, "column": 17, "nodeType": "380", "endLine": 346, "endColumn": 19}, {"ruleId": "322", "severity": 2, "message": "389", "line": 6, "column": 10, "nodeType": null, "messageId": "324", "endLine": 6, "endColumn": 14}, {"ruleId": "322", "severity": 2, "message": "390", "line": 6, "column": 16, "nodeType": null, "messageId": "324", "endLine": 6, "endColumn": 27}, {"ruleId": "322", "severity": 2, "message": "391", "line": 12, "column": 3, "nodeType": null, "messageId": "324", "endLine": 12, "endColumn": 13}, {"ruleId": "322", "severity": 2, "message": "392", "line": 13, "column": 3, "nodeType": null, "messageId": "324", "endLine": 13, "endColumn": 14}, {"ruleId": "322", "severity": 2, "message": "393", "line": 16, "column": 7, "nodeType": null, "messageId": "324", "endLine": 16, "endColumn": 12}, {"ruleId": "322", "severity": 2, "message": "394", "line": 43, "column": 7, "nodeType": null, "messageId": "324", "endLine": 43, "endColumn": 15}, {"ruleId": "322", "severity": 2, "message": "382", "line": 11, "column": 10, "nodeType": null, "messageId": "324", "endLine": 11, "endColumn": 15}, {"ruleId": "326", "severity": 2, "message": "327", "line": 224, "column": 32, "nodeType": "328", "messageId": "329", "endLine": 224, "endColumn": 35, "suggestions": "395"}, {"ruleId": "326", "severity": 2, "message": "327", "line": 96, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 96, "endColumn": 24, "suggestions": "396"}, {"ruleId": "326", "severity": 2, "message": "327", "line": 116, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 116, "endColumn": 24, "suggestions": "397"}, {"ruleId": "326", "severity": 2, "message": "327", "line": 141, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 141, "endColumn": 24, "suggestions": "398"}, {"ruleId": "322", "severity": 2, "message": "399", "line": 8, "column": 3, "nodeType": null, "messageId": "324", "endLine": 8, "endColumn": 12}, {"ruleId": "322", "severity": 2, "message": "400", "line": 11, "column": 3, "nodeType": null, "messageId": "324", "endLine": 11, "endColumn": 8}, {"ruleId": "322", "severity": 2, "message": "401", "line": 12, "column": 3, "nodeType": null, "messageId": "324", "endLine": 12, "endColumn": 8}, {"ruleId": "322", "severity": 2, "message": "402", "line": 14, "column": 3, "nodeType": null, "messageId": "324", "endLine": 14, "endColumn": 12}, {"ruleId": "326", "severity": 2, "message": "327", "line": 50, "column": 23, "nodeType": "328", "messageId": "329", "endLine": 50, "endColumn": 26, "suggestions": "403"}, {"ruleId": "326", "severity": 2, "message": "327", "line": 185, "column": 23, "nodeType": "328", "messageId": "329", "endLine": 185, "endColumn": 26, "suggestions": "404"}, "@typescript-eslint/no-unused-vars", "'Upload' is defined but never used.", "unusedVar", "'AlertCircle' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["405", "406"], ["407", "408"], "'ShoppingCart' is defined but never used.", "'Settings' is defined but never used.", "'BarChart3' is defined but never used.", "'customRequests' is assigned a value but never used.", "'recentUsers' is assigned a value but never used.", ["409", "410"], "'handleUpdateRequestStatus' is assigned a value but never used.", ["411", "412"], ["413", "414"], "'Input' is defined but never used.", "'Label' is defined but never used.", "'Shield' is defined but never used.", "'adminLoading' is assigned a value but never used.", "'adminSuccess' is assigned a value but never used.", "'adminError' is assigned a value but never used.", "'setUserEmail' is assigned a value but never used.", ["415", "416"], "'handleMakeAdmin' is assigned a value but never used.", ["417", "418"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["419", "420", "421", "422"], ["423", "424", "425", "426"], ["427", "428"], ["429", "430"], "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", ["431", "432", "433", "434"], ["435", "436", "437", "438"], "'Zap' is defined but never used.", ["439", "440"], ["441", "442", "443", "444"], ["445", "446", "447", "448"], ["449", "450", "451", "452"], "'Clock' is defined but never used.", "'DollarSign' is defined but never used.", "'FileText' is defined but never used.", "'Lightbulb' is defined but never used.", "'Award' is defined but never used.", "'userData' is assigned a value but never used.", ["453", "454"], ["455", "456", "457", "458"], ["459", "460", "461", "462"], ["463", "464", "465", "466"], ["467", "468", "469", "470"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["471", "472", "473", "474"], "'Badge' is defined but never used.", ["475", "476"], ["477", "478", "479", "480"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterAndSortTemplates'. Either include it or remove the dependency array.", "ArrayExpression", ["481"], "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'ArrowRight' is defined but never used.", "'CheckCircle' is defined but never used.", "'stats' is assigned a value but never used.", "'features' is assigned a value but never used.", ["482", "483"], ["484", "485"], ["486", "487"], ["488", "489"], "'deleteDoc' is defined but never used.", "'limit' is defined but never used.", "'where' is defined but never used.", "'Timestamp' is defined but never used.", ["490", "491"], ["492", "493"], {"messageId": "494", "fix": "495", "desc": "496"}, {"messageId": "497", "fix": "498", "desc": "499"}, {"messageId": "494", "fix": "500", "desc": "496"}, {"messageId": "497", "fix": "501", "desc": "499"}, {"messageId": "494", "fix": "502", "desc": "496"}, {"messageId": "497", "fix": "503", "desc": "499"}, {"messageId": "494", "fix": "504", "desc": "496"}, {"messageId": "497", "fix": "505", "desc": "499"}, {"messageId": "494", "fix": "506", "desc": "496"}, {"messageId": "497", "fix": "507", "desc": "499"}, {"messageId": "494", "fix": "508", "desc": "496"}, {"messageId": "497", "fix": "509", "desc": "499"}, {"messageId": "494", "fix": "510", "desc": "496"}, {"messageId": "497", "fix": "511", "desc": "499"}, {"messageId": "512", "data": "513", "fix": "514", "desc": "515"}, {"messageId": "512", "data": "516", "fix": "517", "desc": "518"}, {"messageId": "512", "data": "519", "fix": "520", "desc": "521"}, {"messageId": "512", "data": "522", "fix": "523", "desc": "524"}, {"messageId": "512", "data": "525", "fix": "526", "desc": "515"}, {"messageId": "512", "data": "527", "fix": "528", "desc": "518"}, {"messageId": "512", "data": "529", "fix": "530", "desc": "521"}, {"messageId": "512", "data": "531", "fix": "532", "desc": "524"}, {"messageId": "494", "fix": "533", "desc": "496"}, {"messageId": "497", "fix": "534", "desc": "499"}, {"messageId": "494", "fix": "535", "desc": "496"}, {"messageId": "497", "fix": "536", "desc": "499"}, {"messageId": "512", "data": "537", "fix": "538", "desc": "539"}, {"messageId": "512", "data": "540", "fix": "541", "desc": "542"}, {"messageId": "512", "data": "543", "fix": "544", "desc": "545"}, {"messageId": "512", "data": "546", "fix": "547", "desc": "548"}, {"messageId": "512", "data": "549", "fix": "550", "desc": "539"}, {"messageId": "512", "data": "551", "fix": "552", "desc": "542"}, {"messageId": "512", "data": "553", "fix": "554", "desc": "545"}, {"messageId": "512", "data": "555", "fix": "556", "desc": "548"}, {"messageId": "494", "fix": "557", "desc": "496"}, {"messageId": "497", "fix": "558", "desc": "499"}, {"messageId": "512", "data": "559", "fix": "560", "desc": "539"}, {"messageId": "512", "data": "561", "fix": "562", "desc": "542"}, {"messageId": "512", "data": "563", "fix": "564", "desc": "545"}, {"messageId": "512", "data": "565", "fix": "566", "desc": "548"}, {"messageId": "512", "data": "567", "fix": "568", "desc": "539"}, {"messageId": "512", "data": "569", "fix": "570", "desc": "542"}, {"messageId": "512", "data": "571", "fix": "572", "desc": "545"}, {"messageId": "512", "data": "573", "fix": "574", "desc": "548"}, {"messageId": "512", "data": "575", "fix": "576", "desc": "539"}, {"messageId": "512", "data": "577", "fix": "578", "desc": "542"}, {"messageId": "512", "data": "579", "fix": "580", "desc": "545"}, {"messageId": "512", "data": "581", "fix": "582", "desc": "548"}, {"messageId": "494", "fix": "583", "desc": "496"}, {"messageId": "497", "fix": "584", "desc": "499"}, {"messageId": "512", "data": "585", "fix": "586", "desc": "539"}, {"messageId": "512", "data": "587", "fix": "588", "desc": "542"}, {"messageId": "512", "data": "589", "fix": "590", "desc": "545"}, {"messageId": "512", "data": "591", "fix": "592", "desc": "548"}, {"messageId": "512", "data": "593", "fix": "594", "desc": "539"}, {"messageId": "512", "data": "595", "fix": "596", "desc": "542"}, {"messageId": "512", "data": "597", "fix": "598", "desc": "545"}, {"messageId": "512", "data": "599", "fix": "600", "desc": "548"}, {"messageId": "512", "data": "601", "fix": "602", "desc": "539"}, {"messageId": "512", "data": "603", "fix": "604", "desc": "542"}, {"messageId": "512", "data": "605", "fix": "606", "desc": "545"}, {"messageId": "512", "data": "607", "fix": "608", "desc": "548"}, {"messageId": "512", "data": "609", "fix": "610", "desc": "539"}, {"messageId": "512", "data": "611", "fix": "612", "desc": "542"}, {"messageId": "512", "data": "613", "fix": "614", "desc": "545"}, {"messageId": "512", "data": "615", "fix": "616", "desc": "548"}, {"messageId": "512", "data": "617", "fix": "618", "desc": "539"}, {"messageId": "512", "data": "619", "fix": "620", "desc": "542"}, {"messageId": "512", "data": "621", "fix": "622", "desc": "545"}, {"messageId": "512", "data": "623", "fix": "624", "desc": "548"}, {"messageId": "494", "fix": "625", "desc": "496"}, {"messageId": "497", "fix": "626", "desc": "499"}, {"messageId": "512", "data": "627", "fix": "628", "desc": "539"}, {"messageId": "512", "data": "629", "fix": "630", "desc": "542"}, {"messageId": "512", "data": "631", "fix": "632", "desc": "545"}, {"messageId": "512", "data": "633", "fix": "634", "desc": "548"}, {"desc": "635", "fix": "636"}, {"messageId": "494", "fix": "637", "desc": "496"}, {"messageId": "497", "fix": "638", "desc": "499"}, {"messageId": "494", "fix": "639", "desc": "496"}, {"messageId": "497", "fix": "640", "desc": "499"}, {"messageId": "494", "fix": "641", "desc": "496"}, {"messageId": "497", "fix": "642", "desc": "499"}, {"messageId": "494", "fix": "643", "desc": "496"}, {"messageId": "497", "fix": "644", "desc": "499"}, {"messageId": "494", "fix": "645", "desc": "496"}, {"messageId": "497", "fix": "646", "desc": "499"}, {"messageId": "494", "fix": "647", "desc": "496"}, {"messageId": "497", "fix": "648", "desc": "499"}, "suggestUnknown", {"range": "649", "text": "650"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "651", "text": "652"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "653", "text": "650"}, {"range": "654", "text": "652"}, {"range": "655", "text": "650"}, {"range": "656", "text": "652"}, {"range": "657", "text": "650"}, {"range": "658", "text": "652"}, {"range": "659", "text": "650"}, {"range": "660", "text": "652"}, {"range": "661", "text": "650"}, {"range": "662", "text": "652"}, {"range": "663", "text": "650"}, {"range": "664", "text": "652"}, "replaceWithAlt", {"alt": "665"}, {"range": "666", "text": "667"}, "Replace with `&quot;`.", {"alt": "668"}, {"range": "669", "text": "670"}, "Replace with `&ldquo;`.", {"alt": "671"}, {"range": "672", "text": "673"}, "Replace with `&#34;`.", {"alt": "674"}, {"range": "675", "text": "676"}, "Replace with `&rdquo;`.", {"alt": "665"}, {"range": "677", "text": "678"}, {"alt": "668"}, {"range": "679", "text": "680"}, {"alt": "671"}, {"range": "681", "text": "682"}, {"alt": "674"}, {"range": "683", "text": "684"}, {"range": "685", "text": "650"}, {"range": "686", "text": "652"}, {"range": "687", "text": "650"}, {"range": "688", "text": "652"}, {"alt": "689"}, {"range": "690", "text": "691"}, "Replace with `&apos;`.", {"alt": "692"}, {"range": "693", "text": "694"}, "Replace with `&lsquo;`.", {"alt": "695"}, {"range": "696", "text": "697"}, "Replace with `&#39;`.", {"alt": "698"}, {"range": "699", "text": "700"}, "Replace with `&rsquo;`.", {"alt": "689"}, {"range": "701", "text": "702"}, {"alt": "692"}, {"range": "703", "text": "704"}, {"alt": "695"}, {"range": "705", "text": "706"}, {"alt": "698"}, {"range": "707", "text": "708"}, {"range": "709", "text": "650"}, {"range": "710", "text": "652"}, {"alt": "689"}, {"range": "711", "text": "712"}, {"alt": "692"}, {"range": "713", "text": "714"}, {"alt": "695"}, {"range": "715", "text": "716"}, {"alt": "698"}, {"range": "717", "text": "718"}, {"alt": "689"}, {"range": "719", "text": "720"}, {"alt": "692"}, {"range": "721", "text": "722"}, {"alt": "695"}, {"range": "723", "text": "724"}, {"alt": "698"}, {"range": "725", "text": "726"}, {"alt": "689"}, {"range": "727", "text": "728"}, {"alt": "692"}, {"range": "729", "text": "730"}, {"alt": "695"}, {"range": "731", "text": "732"}, {"alt": "698"}, {"range": "733", "text": "734"}, {"range": "735", "text": "650"}, {"range": "736", "text": "652"}, {"alt": "689"}, {"range": "737", "text": "738"}, {"alt": "692"}, {"range": "739", "text": "740"}, {"alt": "695"}, {"range": "741", "text": "742"}, {"alt": "698"}, {"range": "743", "text": "744"}, {"alt": "689"}, {"range": "745", "text": "746"}, {"alt": "692"}, {"range": "747", "text": "748"}, {"alt": "695"}, {"range": "749", "text": "750"}, {"alt": "698"}, {"range": "751", "text": "752"}, {"alt": "689"}, {"range": "753", "text": "754"}, {"alt": "692"}, {"range": "755", "text": "756"}, {"alt": "695"}, {"range": "757", "text": "758"}, {"alt": "698"}, {"range": "759", "text": "760"}, {"alt": "689"}, {"range": "761", "text": "762"}, {"alt": "692"}, {"range": "763", "text": "764"}, {"alt": "695"}, {"range": "765", "text": "766"}, {"alt": "698"}, {"range": "767", "text": "768"}, {"alt": "689"}, {"range": "769", "text": "770"}, {"alt": "692"}, {"range": "771", "text": "772"}, {"alt": "695"}, {"range": "773", "text": "774"}, {"alt": "698"}, {"range": "775", "text": "776"}, {"range": "777", "text": "650"}, {"range": "778", "text": "652"}, {"alt": "689"}, {"range": "779", "text": "780"}, {"alt": "692"}, {"range": "781", "text": "782"}, {"alt": "695"}, {"range": "783", "text": "784"}, {"alt": "698"}, {"range": "785", "text": "786"}, "Update the dependencies array to be: [templates, searchTerm, selectedCategory, sortBy, filterAndSortTemplates]", {"range": "787", "text": "788"}, {"range": "789", "text": "650"}, {"range": "790", "text": "652"}, {"range": "791", "text": "650"}, {"range": "792", "text": "652"}, {"range": "793", "text": "650"}, {"range": "794", "text": "652"}, {"range": "795", "text": "650"}, {"range": "796", "text": "652"}, {"range": "797", "text": "650"}, {"range": "798", "text": "652"}, {"range": "799", "text": "650"}, {"range": "800", "text": "652"}, [1458, 1461], "unknown", [1458, 1461], "never", [2225, 2228], [2225, 2228], [2407, 2410], [2407, 2410], [3339, 3342], [3339, 3342], [3726, 3729], [3726, 3729], [1182, 1185], [1182, 1185], [1810, 1813], [1810, 1813], "&quot;", [4665, 4760], "Go to Firebase Console → Firestore → users collection → find your user → change role to &quot;admin\"", "&ldquo;", [4665, 4760], "Go to Firebase Console → Firestore → users collection → find your user → change role to &ldquo;admin\"", "&#34;", [4665, 4760], "Go to Firebase Console → Firestore → users collection → find your user → change role to &#34;admin\"", "&rdquo;", [4665, 4760], "Go to Firebase Console → Firestore → users collection → find your user → change role to &rdquo;admin\"", [4665, 4760], "Go to Firebase Console → Firestore → users collection → find your user → change role to \"admin&quot;", [4665, 4760], "Go to Firebase Console → Firestore → users collection → find your user → change role to \"admin&ldquo;", [4665, 4760], "Go to Firebase Console → Firestore → users collection → find your user → change role to \"admin&#34;", [4665, 4760], "Go to Firebase Console → Firestore → users collection → find your user → change role to \"admin&rdquo;", [1883, 1886], [1883, 1886], [3056, 3059], [3056, 3059], "&apos;", [8275, 8334], "\n            Can&apos;t Find What You're Looking For?\n          ", "&lsquo;", [8275, 8334], "\n            Can&lsquo;t Find What You're Looking For?\n          ", "&#39;", [8275, 8334], "\n            Can&#39;t Find What You're Looking For?\n          ", "&rsquo;", [8275, 8334], "\n            Can&rsquo;t Find What You're Looking For?\n          ", [8275, 8334], "\n            Can't Find What You&apos;re Looking For?\n          ", [8275, 8334], "\n            Can't Find What You&lsquo;re Looking For?\n          ", [8275, 8334], "\n            Can't Find What You&#39;re Looking For?\n          ", [8275, 8334], "\n            Can't Find What You&rsquo;re Looking For?\n          ", [2915, 2918], [2915, 2918], [3429, 3524], "\n              Thank you for contacting us. We&apos;ll get back to you within 24 hours.\n            ", [3429, 3524], "\n              Thank you for contacting us. We&lsquo;ll get back to you within 24 hours.\n            ", [3429, 3524], "\n              Thank you for contacting us. We&#39;ll get back to you within 24 hours.\n            ", [3429, 3524], "\n              Thank you for contacting us. We&rsquo;ll get back to you within 24 hours.\n            ", [4119, 4225], "\n          Have questions about our templates or need help with your project? We&apos;re here to help!\n        ", [4119, 4225], "\n          Have questions about our templates or need help with your project? We&lsquo;re here to help!\n        ", [4119, 4225], "\n          Have questions about our templates or need help with your project? We&#39;re here to help!\n        ", [4119, 4225], "\n          Have questions about our templates or need help with your project? We&rsquo;re here to help!\n        ", [4491, 4593], "\n                Fill out the form below and we&apos;ll get back to you as soon as possible.\n              ", [4491, 4593], "\n                Fill out the form below and we&lsquo;ll get back to you as soon as possible.\n              ", [4491, 4593], "\n                Fill out the form below and we&#39;ll get back to you as soon as possible.\n              ", [4491, 4593], "\n                Fill out the form below and we&rsquo;ll get back to you as soon as possible.\n              ", [3145, 3148], [3145, 3148], [5046, 5179], "\n          Can&apos;t find what you're looking for? Let our expert team create a custom template tailored to your specific needs.\n        ", [5046, 5179], "\n          Can&lsquo;t find what you're looking for? Let our expert team create a custom template tailored to your specific needs.\n        ", [5046, 5179], "\n          Can&#39;t find what you're looking for? Let our expert team create a custom template tailored to your specific needs.\n        ", [5046, 5179], "\n          Can&rsquo;t find what you're looking for? Let our expert team create a custom template tailored to your specific needs.\n        ", [5046, 5179], "\n          Can't find what you&apos;re looking for? Let our expert team create a custom template tailored to your specific needs.\n        ", [5046, 5179], "\n          Can't find what you&lsquo;re looking for? Let our expert team create a custom template tailored to your specific needs.\n        ", [5046, 5179], "\n          Can't find what you&#39;re looking for? Let our expert team create a custom template tailored to your specific needs.\n        ", [5046, 5179], "\n          Can't find what you&rsquo;re looking for? Let our expert team create a custom template tailored to your specific needs.\n        ", [5444, 5559], "\n                Tell us about your project requirements and we&apos;ll create something amazing for you.\n              ", [5444, 5559], "\n                Tell us about your project requirements and we&lsquo;ll create something amazing for you.\n              ", [5444, 5559], "\n                Tell us about your project requirements and we&#39;ll create something amazing for you.\n              ", [5444, 5559], "\n                Tell us about your project requirements and we&rsquo;ll create something amazing for you.\n              ", [11617, 11659], "We&apos;ll review and send you a detailed quote", [11617, 11659], "We&lsquo;ll review and send you a detailed quote", [11617, 11659], "We&#39;ll review and send you a detailed quote", [11617, 11659], "We&rsquo;ll review and send you a detailed quote", [9805, 9931], "\n              If you have any questions about your orders or need assistance with downloads, we&apos;re here to help.\n            ", [9805, 9931], "\n              If you have any questions about your orders or need assistance with downloads, we&lsquo;re here to help.\n            ", [9805, 9931], "\n              If you have any questions about your orders or need assistance with downloads, we&#39;re here to help.\n            ", [9805, 9931], "\n              If you have any questions about your orders or need assistance with downloads, we&rsquo;re here to help.\n            ", [3450, 3453], [3450, 3453], [3143, 3159], "What&apos;s Included:", [3143, 3159], "What&lsquo;s Included:", [3143, 3159], "What&#39;s Included:", [3143, 3159], "What&rsquo;s Included:", [1915, 1964], "[templates, searchTerm, selectedCategory, sortBy, filterAndSortTemplates]", [8213, 8216], [8213, 8216], [2862, 2865], [2862, 2865], [3441, 3444], [3441, 3444], [4021, 4024], [4021, 4024], [1277, 1280], [1277, 1280], [5052, 5055], [5052, 5055]]