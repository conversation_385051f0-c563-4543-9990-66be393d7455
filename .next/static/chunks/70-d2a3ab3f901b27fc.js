"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[70],{283:(e,t,r)=>{r.d(t,{A:()=>l,AuthProvider:()=>c});var a=r(5155),s=r(2115),i=r(6203),n=r(5317),d=r(6104);let o=(0,s.createContext)(void 0),l=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[r,l]=(0,s.useState)(null),[c,u]=(0,s.useState)(null),[g,p]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=(0,i.hg)(d.j,async e=>{if(e){l(e);try{let t=await (0,n.x7)((0,n.H9)(d.db,"users",e.uid));if(t.exists())u(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",e.uid),t),u(t)}}catch(t){console.error("Error fetching/creating user data:",t),u({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else l(null),u(null);p(!1)});return()=>e()},[]);let v=async(e,t)=>{try{await (0,i.x9)(d.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},f=async(e,t)=>{try{let{user:r}=await (0,i.eJ)(d.j,e,t),a={uid:r.uid,email:r.email,role:"user",displayName:r.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",r.uid),a)}catch(e){throw console.error("Sign up error:",e),e}},h=async()=>{await (0,i.CI)(d.j)},m=async e=>{if(!r)throw Error("No user logged in");try{let t={...e,updatedAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",r.uid),t,{merge:!0}),c&&u({...c,...t})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,a.jsx)(o.Provider,{value:{user:r,userData:c,loading:g,signIn:v,signUp:f,logout:h,updateUserProfile:m},children:t})}},285:(e,t,r)=>{r.d(t,{$:()=>o});var a=r(5155);r(2115);var s=r(9708),i=r(2085),n=r(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:i,asChild:o=!1,...l}=e,c=o?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:r,size:i,className:t})),...l})}},1788:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2288:(e,t,r)=>{r.d(t,{CF:()=>o,CV:()=>u,FQ:()=>p,KV:()=>n,SZ:()=>v,Tb:()=>g,Xm:()=>c,gW:()=>d,hx:()=>i,wx:()=>l});var a=r(5317),s=r(6104);let i=async e=>{try{return(await (0,a.gS)((0,a.rJ)(s.db,"customRequests"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating custom request:",e),e}},n=async()=>{try{let e=(0,a.P)((0,a.rJ)(s.db,"customRequests"),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching custom requests:",e),e}},d=async(e,t,r)=>{try{let i={status:t,updatedAt:new Date};r&&(i.adminNotes=r),await (0,a.mZ)((0,a.H9)(s.db,"customRequests",e),i)}catch(e){throw console.error("Error updating custom request:",e),e}},o=async()=>{try{let e=(0,a.P)((0,a.rJ)(s.db,"users"),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching users:",e),e}},l=async()=>{try{let[e,t,r]=await Promise.all([(0,a.GG)((0,a.rJ)(s.db,"users")),(0,a.GG)((0,a.rJ)(s.db,"templates")),(0,a.GG)((0,a.rJ)(s.db,"customRequests"))]),i=e.size,n=t.size,d=r.size,o=r.docs.filter(e=>"pending"===e.data().status).length;return{totalUsers:i,totalTemplates:n,totalRequests:d,pendingRequests:o,totalSales:0,customizations:0}}catch(e){throw console.error("Error fetching dashboard stats:",e),e}},c=e=>{let t=(0,a.P)((0,a.rJ)(s.db,"customRequests"),(0,a.My)("createdAt","desc"));return(0,a.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})},u=async e=>{try{return(await (0,a.gS)((0,a.rJ)(s.db,"contactMessages"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating contact message:",e),e}},g=async()=>{try{let e=(0,a.P)((0,a.rJ)(s.db,"contactMessages"),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching contact messages:",e),e}},p=async(e,t,r)=>{try{let i={status:t,updatedAt:new Date};r&&(i.adminNotes=r),await (0,a.mZ)((0,a.H9)(s.db,"contactMessages",e),i)}catch(e){throw console.error("Error updating contact message:",e),e}},v=e=>{let t=(0,a.P)((0,a.rJ)(s.db,"contactMessages"),(0,a.My)("createdAt","desc"));return(0,a.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})}},2523:(e,t,r)=>{r.d(t,{p:()=>i});var a=r(5155);r(2115);var s=r(9434);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},3786:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},6104:(e,t,r)=>{r.d(t,{db:()=>o,j:()=>d});var a=r(3915),s=r(6203),i=r(5317);let n=(0,a.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),d=(0,s.xI)(n),o=(0,i.aU)(n)},6126:(e,t,r)=>{r.d(t,{E:()=>o});var a=r(5155);r(2115);var s=r(9708),i=r(2085),n=r(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:i=!1,...o}=e,l=i?s.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:r}),t),...o})}},6695:(e,t,r)=>{r.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=r(5155);r(2115);var s=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},6932:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7924:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8564:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9409:(e,t,r)=>{r.d(t,{bq:()=>u,eb:()=>p,gC:()=>g,l6:()=>l,yv:()=>c});var a=r(5155);r(2115);var s=r(8715),i=r(6474),n=r(5196),d=r(7863),o=r(9434);function l(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,a.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:r="default",children:n,...d}=e;return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d,children:[n,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function g(e){let{className:t,children:r,position:i="popper",...n}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,a.jsx)(v,{}),(0,a.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,a.jsx)(f,{})]})})}function p(e){let{className:t,children:r,...i}=e;return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:r})]})}function v(e){let{className:t,...r}=e;return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(d.A,{className:"size-4"})})}function f(e){let{className:t,...r}=e;return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"size-4"})})}},9434:(e,t,r)=>{r.d(t,{cn:()=>i});var a=r(2596),s=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}}]);