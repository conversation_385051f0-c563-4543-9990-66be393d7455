(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[107],{381:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},488:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},1007:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1414:(e,r,t)=>{"use strict";e.exports=t(2436)},2436:(e,r,t)=>{"use strict";var n=t(2115),o="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},a=n.useState,l=n.useEffect,u=n.useLayoutEffect,i=n.useDebugValue;function s(e){var r=e.getSnapshot;e=e.value;try{var t=r();return!o(e,t)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var t=r(),n=a({inst:{value:t,getSnapshot:r}}),o=n[0].inst,c=n[1];return u(function(){o.value=t,o.getSnapshot=r,s(o)&&c({inst:o})},[e,t,r]),l(function(){return s(o)&&c({inst:o}),e(function(){s(o)&&c({inst:o})})},[e]),i(t),t};r.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},2894:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},4011:(e,r,t)=>{"use strict";t.d(r,{H4:()=>k,bL:()=>b});var n=t(2115),o=t(6081),a=t(9033),l=t(2712),u=t(3655),i=t(1414);function s(){return()=>{}}var c=t(5155),d="Avatar",[f,p]=(0,o.A)(d),[v,m]=f(d),h=n.forwardRef((e,r)=>{let{__scopeAvatar:t,...o}=e,[a,l]=n.useState("idle");return(0,c.jsx)(v,{scope:t,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,c.jsx)(u.sG.span,{...o,ref:r})})});h.displayName=d;var w="AvatarImage";n.forwardRef((e,r)=>{let{__scopeAvatar:t,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(w,t),v=function(e,r){let{referrerPolicy:t,crossOrigin:o}=r,a=(0,i.useSyncExternalStore)(s,()=>!0,()=>!1),u=n.useRef(null),c=a?(u.current||(u.current=new window.Image),u.current):null,[d,f]=n.useState(()=>x(c,e));return(0,l.N)(()=>{f(x(c,e))},[c,e]),(0,l.N)(()=>{let e=e=>()=>{f(e)};if(!c)return;let r=e("loaded"),n=e("error");return c.addEventListener("load",r),c.addEventListener("error",n),t&&(c.referrerPolicy=t),"string"==typeof o&&(c.crossOrigin=o),()=>{c.removeEventListener("load",r),c.removeEventListener("error",n)}},[c,o,t]),d}(o,f),h=(0,a.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==v&&h(v)},[v,h]),"loaded"===v?(0,c.jsx)(u.sG.img,{...f,ref:r,src:o}):null}).displayName=w;var g="AvatarFallback",y=n.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:o,...a}=e,l=m(g,t),[i,s]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),i&&"loaded"!==l.imageLoadingStatus?(0,c.jsx)(u.sG.span,{...a,ref:r}):null});function x(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}y.displayName=g;var b=h,k=y},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},4516:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4783:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5684:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},6151:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},7340:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},8175:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},8698:(e,r,t)=>{"use strict";t.d(r,{UC:()=>eX,q7:()=>eZ,ZL:()=>eH,bL:()=>eU,wv:()=>eW,l9:()=>eq});var n=t(2115),o=t(5185),a=t(6101),l=t(6081),u=t(5845),i=t(3655),s=t(7328),c=t(4315),d=t(9178),f=t(2293),p=t(7900),v=t(1285),m=t(5152),h=t(4378),w=t(8905),g=t(9196),y=t(9708),x=t(9033),b=t(8168),k=t(1114),M=t(5155),C=["Enter"," "],j=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",...j],_={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},D="Menu",[E,S,I]=(0,s.N)(D),[N,P]=(0,l.A)(D,[I,m.Bk,g.RG]),L=(0,m.Bk)(),T=(0,g.RG)(),[F,O]=N(D),[G,K]=N(D),z=e=>{let{__scopeMenu:r,open:t=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=L(r),[s,d]=n.useState(null),f=n.useRef(!1),p=(0,x.c)(l),v=(0,c.jH)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,M.jsx)(m.bL,{...i,children:(0,M.jsx)(F,{scope:r,open:t,onOpenChange:p,content:s,onContentChange:d,children:(0,M.jsx)(G,{scope:r,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:u,children:o})})})};z.displayName=D;var B=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=L(t);return(0,M.jsx)(m.Mz,{...o,...n,ref:r})});B.displayName="MenuAnchor";var V="MenuPortal",[U,q]=N(V,{forceMount:void 0}),H=e=>{let{__scopeMenu:r,forceMount:t,children:n,container:o}=e,a=O(V,r);return(0,M.jsx)(U,{scope:r,forceMount:t,children:(0,M.jsx)(w.C,{present:t||a.open,children:(0,M.jsx)(h.Z,{asChild:!0,container:o,children:n})})})};H.displayName=V;var X="MenuContent",[Z,W]=N(X),Y=n.forwardRef((e,r)=>{let t=q(X,e.__scopeMenu),{forceMount:n=t.forceMount,...o}=e,a=O(X,e.__scopeMenu),l=K(X,e.__scopeMenu);return(0,M.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(w.C,{present:n||a.open,children:(0,M.jsx)(E.Slot,{scope:e.__scopeMenu,children:l.modal?(0,M.jsx)(J,{...o,ref:r}):(0,M.jsx)(Q,{...o,ref:r})})})})}),J=n.forwardRef((e,r)=>{let t=O(X,e.__scopeMenu),l=n.useRef(null),u=(0,a.s)(r,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,M.jsx)(ee,{...e,ref:u,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),Q=n.forwardRef((e,r)=>{let t=O(X,e.__scopeMenu);return(0,M.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),$=(0,y.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,r)=>{let{__scopeMenu:t,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:C,..._}=e,A=O(X,t),D=K(X,t),E=L(t),I=T(t),N=S(t),[P,F]=n.useState(null),G=n.useRef(null),z=(0,a.s)(r,G,A.onContentChange),B=n.useRef(0),V=n.useRef(""),U=n.useRef(0),q=n.useRef(null),H=n.useRef("right"),W=n.useRef(0),Y=C?k.A:n.Fragment,J=e=>{var r,t;let n=V.current+e,o=N().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,t){var n;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=t?e.indexOf(t):-1,l=(n=Math.max(a,0),e.map((r,t)=>e[(n+t)%e.length]));1===o.length&&(l=l.filter(e=>e!==t));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==t?u:void 0}(o.map(e=>e.textValue),n,l),i=null==(t=o.find(e=>e.textValue===u))?void 0:t.ref.current;!function e(r){V.current=r,window.clearTimeout(B.current),""!==r&&(B.current=window.setTimeout(()=>e(""),1e3))}(n),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,f.Oh)();let Q=n.useCallback(e=>{var r,t;return H.current===(null==(r=q.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:t,y:n}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,s=l.y,c=u.x,d=u.y;s>n!=d>n&&t<(c-i)*(n-s)/(d-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(t=q.current)?void 0:t.area)},[]);return(0,M.jsx)(Z,{scope:t,searchRef:V,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{var r;Q(e)||(null==(r=G.current)||r.focus(),F(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:U,onPointerGraceIntentChange:n.useCallback(e=>{q.current=e},[]),children:(0,M.jsx)(Y,{...C?{as:$,allowPinchZoom:!0}:void 0,children:(0,M.jsx)(p.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=G.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,M.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,M.jsx)(g.bL,{asChild:!0,...I,dir:D.dir,orientation:"vertical",loop:l,currentTabStopId:P,onCurrentTabStopIdChange:F,onEntryFocus:(0,o.m)(v,e=>{D.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eR(A.open),"data-radix-menu-content":"",dir:D.dir,...E,..._,ref:z,style:{outline:"none",..._.style},onKeyDown:(0,o.m)(_.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!t&&n&&J(e.key));let o=G.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let t of e)if(t===r||(t.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eD(e=>{let r=e.target,t=W.current!==e.clientX;e.currentTarget.contains(r)&&t&&(H.current=e.clientX>W.current?"right":"left",W.current=e.clientX)}))})})})})})})});Y.displayName=X;var er=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{role:"group",...n,ref:r})});er.displayName="MenuGroup";var et=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{...n,ref:r})});et.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,r)=>{let{disabled:t=!1,onSelect:l,...u}=e,s=n.useRef(null),c=K(en,e.__scopeMenu),d=W(en,e.__scopeMenu),f=(0,a.s)(r,s),p=n.useRef(!1);return(0,M.jsx)(el,{...u,ref:f,disabled:t,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!t&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:r=>{var t;null==(t=e.onPointerDown)||t.call(e,r),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;p.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==d.searchRef.current;t||r&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var el=n.forwardRef((e,r)=>{let{__scopeMenu:t,disabled:l=!1,textValue:u,...s}=e,c=W(en,t),d=T(t),f=n.useRef(null),p=(0,a.s)(r,f),[v,m]=n.useState(!1),[h,w]=n.useState("");return n.useEffect(()=>{let e=f.current;if(e){var r;w((null!=(r=e.textContent)?r:"").trim())}},[s.children]),(0,M.jsx)(E.ItemSlot,{scope:t,disabled:l,textValue:null!=u?u:h,children:(0,M.jsx)(g.q7,{asChild:!0,...d,focusable:!l,children:(0,M.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eD(e=>{l?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eD(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eu=n.forwardRef((e,r)=>{let{checked:t=!1,onCheckedChange:n,...a}=e;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:t,children:(0,M.jsx)(ea,{role:"menuitemcheckbox","aria-checked":e_(t)?"mixed":t,...a,ref:r,"data-state":eA(t),onSelect:(0,o.m)(a.onSelect,()=>null==n?void 0:n(!!e_(t)||!t),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[es,ec]=N(ei,{value:void 0,onValueChange:()=>{}}),ed=n.forwardRef((e,r)=>{let{value:t,onValueChange:n,...o}=e,a=(0,x.c)(n);return(0,M.jsx)(es,{scope:e.__scopeMenu,value:t,onValueChange:a,children:(0,M.jsx)(er,{...o,ref:r})})});ed.displayName=ei;var ef="MenuRadioItem",ep=n.forwardRef((e,r)=>{let{value:t,...n}=e,a=ec(ef,e.__scopeMenu),l=t===a.value;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,M.jsx)(ea,{role:"menuitemradio","aria-checked":l,...n,ref:r,"data-state":eA(l),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,t)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var ev="MenuItemIndicator",[em,eh]=N(ev,{checked:!1}),ew=n.forwardRef((e,r)=>{let{__scopeMenu:t,forceMount:n,...o}=e,a=eh(ev,t);return(0,M.jsx)(w.C,{present:n||e_(a.checked)||!0===a.checked,children:(0,M.jsx)(i.sG.span,{...o,ref:r,"data-state":eA(a.checked)})})});ew.displayName=ev;var eg=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:r})});eg.displayName="MenuSeparator";var ey=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=L(t);return(0,M.jsx)(m.i3,{...o,...n,ref:r})});ey.displayName="MenuArrow";var[ex,eb]=N("MenuSub"),ek="MenuSubTrigger",eM=n.forwardRef((e,r)=>{let t=O(ek,e.__scopeMenu),l=K(ek,e.__scopeMenu),u=eb(ek,e.__scopeMenu),i=W(ek,e.__scopeMenu),s=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=i,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,M.jsx)(B,{asChild:!0,...f,children:(0,M.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":u.contentId,"data-state":eR(t.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var n;null==(n=e.onClick)||n.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eD(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||t.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{t.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eD(e=>{var r,n;p();let o=null==(r=t.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(n=t.content)?void 0:n.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let n=""!==i.searchRef.current;if(!e.disabled&&(!n||" "!==r.key)&&_[l.dir].includes(r.key)){var o;t.onOpenChange(!0),null==(o=t.content)||o.focus(),r.preventDefault()}})})})});eM.displayName=ek;var eC="MenuSubContent",ej=n.forwardRef((e,r)=>{let t=q(X,e.__scopeMenu),{forceMount:l=t.forceMount,...u}=e,i=O(X,e.__scopeMenu),s=K(X,e.__scopeMenu),c=eb(eC,e.__scopeMenu),d=n.useRef(null),f=(0,a.s)(r,d);return(0,M.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(w.C,{present:l||i.open,children:(0,M.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...u,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;s.isUsingKeyboardRef.current&&(null==(r=d.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),t=A[s.dir].includes(e.key);if(r&&t){var n;i.onOpenChange(!1),null==(n=c.trigger)||n.focus(),e.preventDefault()}})})})})})});function eR(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function eA(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eD(e){return r=>"mouse"===r.pointerType?e(r):void 0}ej.displayName=eC;var eE="DropdownMenu",[eS,eI]=(0,l.A)(eE,[P]),eN=P(),[eP,eL]=eS(eE),eT=e=>{let{__scopeDropdownMenu:r,children:t,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,c=eN(r),d=n.useRef(null),[f,p]=(0,u.i)({prop:a,defaultProp:null!=l&&l,onChange:i,caller:eE});return(0,M.jsx)(eP,{scope:r,triggerId:(0,v.B)(),triggerRef:d,contentId:(0,v.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,M.jsx)(z,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:t})})};eT.displayName=eE;var eF="DropdownMenuTrigger",eO=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,disabled:n=!1,...l}=e,u=eL(eF,t),s=eN(t);return(0,M.jsx)(B,{asChild:!0,...s,children:(0,M.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eO.displayName=eF;var eG=e=>{let{__scopeDropdownMenu:r,...t}=e,n=eN(r);return(0,M.jsx)(H,{...n,...t})};eG.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",ez=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,l=eL(eK,t),u=eN(t),i=n.useRef(!1);return(0,M.jsx)(Y,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;(!l.modal||n)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ez.displayName=eK,n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(er,{...o,...n,ref:r})}).displayName="DropdownMenuGroup",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(et,{...o,...n,ref:r})}).displayName="DropdownMenuLabel";var eB=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ea,{...o,...n,ref:r})});eB.displayName="DropdownMenuItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(eu,{...o,...n,ref:r})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ed,{...o,...n,ref:r})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ep,{...o,...n,ref:r})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ew,{...o,...n,ref:r})}).displayName="DropdownMenuItemIndicator";var eV=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(eg,{...o,...n,ref:r})});eV.displayName="DropdownMenuSeparator",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ey,{...o,...n,ref:r})}).displayName="DropdownMenuArrow",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(eM,{...o,...n,ref:r})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ej,{...o,...n,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eU=eT,eq=eO,eH=eG,eX=ez,eZ=eB,eW=eV},8883:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9099:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9196:(e,r,t)=>{"use strict";t.d(r,{RG:()=>b,bL:()=>E,q7:()=>S});var n=t(2115),o=t(5185),a=t(7328),l=t(6101),u=t(6081),i=t(1285),s=t(3655),c=t(9033),d=t(5845),f=t(4315),p=t(5155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,g,y]=(0,a.N)(h),[x,b]=(0,u.A)(h,[y]),[k,M]=x(h),C=n.forwardRef((e,r)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:r})})}));C.displayName=h;var j=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:a,loop:u=!1,dir:i,currentTabStopId:w,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:b,preventScrollOnEntryFocus:M=!1,...C}=e,j=n.useRef(null),R=(0,l.s)(r,j),_=(0,f.jH)(i),[A,E]=(0,d.i)({prop:w,defaultProp:null!=y?y:null,onChange:x,caller:h}),[S,I]=n.useState(!1),N=(0,c.c)(b),P=g(t),L=n.useRef(!1),[T,F]=n.useState(0);return n.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(v,N),()=>e.removeEventListener(v,N)},[N]),(0,p.jsx)(k,{scope:t,orientation:a,dir:_,loop:u,currentTabStopId:A,onItemFocus:n.useCallback(e=>E(e),[E]),onItemShiftTab:n.useCallback(()=>I(!0),[]),onFocusableItemAdd:n.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>F(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:S||0===T?-1:0,"data-orientation":a,...C,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!L.current;if(e.target===e.currentTarget&&r&&!S){let r=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=P().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),M)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>I(!1))})})}),R="RovingFocusGroupItem",_=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:a=!0,active:l=!1,tabStopId:u,children:c,...d}=e,f=(0,i.B)(),v=u||f,m=M(R,t),h=m.currentTabStopId===v,y=g(t),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:k}=m;return n.useEffect(()=>{if(a)return x(),()=>b()},[a,x,b]),(0,p.jsx)(w.ItemSlot,{scope:t,id:v,focusable:a,active:l,children:(0,p.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...d,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let o=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,m.orientation,m.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let n=t.indexOf(e.currentTarget);t=m.loop?function(e,r){return e.map((t,n)=>e[(r+n)%e.length])}(t,n+1):t.slice(n+1)}setTimeout(()=>D(t))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=k}):c})})});_.displayName=R;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var E=C,S=_}}]);