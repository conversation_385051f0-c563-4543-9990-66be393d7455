(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var a=t(5155);t(2115);var r=t(9708),i=t(2085),l=t(9434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:i,asChild:d=!1,...c}=e,o=d?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,l.cn)(n({variant:t,size:i,className:s})),...c})}},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},968:(e,s,t)=>{"use strict";t.d(s,{b:()=>n});var a=t(2115),r=t(3655),i=t(5155),l=a.forwardRef((e,s)=>(0,i.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var n=l},1154:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1497:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},2110:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(5155),r=t(2115),i=t(285),l=t(2523),n=t(5057),d=t(8539),c=t(6695),o=t(5365),u=t(8883),x=t(1497),m=t(9420),h=t(646),p=t(1154),g=t(4516),v=t(4186);let b=(0,t(9946).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),f=[{icon:u.A,title:"Email Support",description:"Get help via email",contact:"<EMAIL>",availability:"24/7 response within 24 hours"},{icon:x.A,title:"Live Chat",description:"Chat with our team",contact:"Available on website",availability:"Mon-Fri, 9 AM - 6 PM EST"},{icon:m.A,title:"Phone Support",description:"Speak directly with us",contact:"+****************",availability:"Mon-Fri, 9 AM - 6 PM EST"}],y=[{question:"How do I download my purchased templates?",answer:"After purchase, you can download your templates from your dashboard. You'll also receive an email with download links."},{question:"Can I customize the templates?",answer:"Yes! All our templates come with full source code and documentation. You can customize them to fit your needs."},{question:"Do you offer refunds?",answer:"We offer a 30-day money-back guarantee if you're not satisfied with your purchase."},{question:"How long does custom design take?",answer:"Custom design projects typically take 5-10 business days, depending on complexity and requirements."}];function j(){let[e,s]=(0,r.useState)(!1),[t,u]=(0,r.useState)(!1),[x,m]=(0,r.useState)(""),[j,N]=(0,r.useState)({name:"",email:"",subject:"",message:""}),w=e=>{N(s=>({...s,[e.target.name]:e.target.value})),m("")},k=async e=>{if(e.preventDefault(),s(!0),m(""),!j.name||!j.email||!j.subject||!j.message){m("Please fill in all fields"),s(!1);return}try{await new Promise(e=>setTimeout(e,2e3)),u(!0)}catch(e){m(e.message||"Failed to send message")}finally{s(!1)}};return t?(0,a.jsx)("div",{className:"container mx-auto px-4 py-20",children:(0,a.jsx)(c.Zp,{className:"max-w-2xl mx-auto text-center",children:(0,a.jsxs)(c.Wu,{className:"p-8",children:[(0,a.jsx)(h.A,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Message Sent!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Thank you for contacting us. We'll get back to you within 24 hours."}),(0,a.jsx)(i.$,{onClick:()=>{u(!1),N({name:"",email:"",subject:"",message:""})},children:"Send Another Message"})]})})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Get in Touch"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Have questions about our templates or need help with your project? We're here to help!"})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Send us a Message"}),(0,a.jsx)(c.BT,{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"name",children:"Name *"}),(0,a.jsx)(l.p,{id:"name",name:"name",placeholder:"Your full name",value:j.name,onChange:w,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"email",children:"Email *"}),(0,a.jsx)(l.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:j.email,onChange:w,required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"subject",children:"Subject *"}),(0,a.jsx)(l.p,{id:"subject",name:"subject",placeholder:"What can we help you with?",value:j.subject,onChange:w,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"message",children:"Message *"}),(0,a.jsx)(d.T,{id:"message",name:"message",placeholder:"Tell us more about your question or request...",value:j.message,onChange:w,rows:6,required:!0})]}),x&&(0,a.jsx)(o.Fc,{variant:"destructive",children:(0,a.jsx)(o.TN,{children:x})}),(0,a.jsxs)(i.$,{type:"submit",className:"w-full",disabled:e,children:[e&&(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Send Message"]})]})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Contact Information"})}),(0,a.jsx)(c.Wu,{className:"space-y-6",children:f.map((e,s)=>{let t=e.icon;return(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(t,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:e.description}),(0,a.jsx)("p",{className:"text-sm font-medium text-blue-600",children:e.contact}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.availability})]})]},s)})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Office Location"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"KaleidoneX HQ"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["123 Design Street",(0,a.jsx)("br",{}),"Creative District",(0,a.jsx)("br",{}),"San Francisco, CA 94102",(0,a.jsx)("br",{}),"United States"]})]})]})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Business Hours"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(v.A,{className:"h-5 w-5 text-purple-600"})}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 mb-2",children:"Support Hours"}),(0,a.jsxs)("div",{className:"space-y-1 text-gray-600",children:[(0,a.jsx)("p",{children:"Monday - Friday: 9:00 AM - 6:00 PM EST"}),(0,a.jsx)("p",{children:"Saturday: 10:00 AM - 4:00 PM EST"}),(0,a.jsx)("p",{children:"Sunday: Closed"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Email support available 24/7"})]})]})})]})]})]}),(0,a.jsxs)("div",{className:"mt-16",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Frequently Asked Questions"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Quick answers to common questions"})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:y.map((e,s)=>(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(b,{className:"h-5 w-5 text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:e.question}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.answer})]})]})})},s))})]})]})}},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(5155);t(2115);var r=t(9434);function i(e){let{className:s,type:t,...i}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},3655:(e,s,t)=>{"use strict";t.d(s,{hO:()=>d,sG:()=>n});var a=t(2115),r=t(7650),i=t(9708),l=t(5155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,i.TL)(`Primitive.${s}`),r=a.forwardRef((e,a)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(r?t:s,{...i,ref:a})});return r.displayName=`Primitive.${s}`,{...e,[s]:r}},{});function d(e,s){e&&r.flushSync(()=>e.dispatchEvent(s))}},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>l});var a=t(5155);t(2115);var r=t(968),i=t(9434);function l(e){let{className:s,...t}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},5365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>n,TN:()=>d});var a=t(5155);t(2115);var r=t(2085),i=t(9434);let l=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(l({variant:t}),s),...r})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...t})}},5731:(e,s,t)=>{Promise.resolve().then(t.bind(t,2110))},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>l});var a=t(5155);t(2115);var r=t(9434);function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}},8539:(e,s,t)=>{"use strict";t.d(s,{T:()=>i});var a=t(5155);t(2115);var r=t(9434);function i(e){let{className:s,...t}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...t})}},8883:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var a=t(2596),r=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[455,441,684,358],()=>s(5731)),_N_E=e.O()}]);