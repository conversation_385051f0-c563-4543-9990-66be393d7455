(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,AuthProvider:()=>c});var a=s(5155),r=s(2115),i=s(6203),n=s(5317),l=s(6104);let o=(0,r.createContext)(void 0),d=()=>{let e=(0,r.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[s,d]=(0,r.useState)(null),[c,x]=(0,r.useState)(null),[m,h]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(l.j,async e=>{if(e){d(e);try{let t=await (0,n.x7)((0,n.H9)(l.db,"users",e.uid));if(t.exists())x(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(l.db,"users",e.uid),t),x(t)}}catch(t){console.error("Error fetching/creating user data:",t),x({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else d(null),x(null);h(!1)});return()=>e()},[]);let u=async(e,t)=>{try{await (0,i.x9)(l.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},f=async(e,t)=>{try{let{user:s}=await (0,i.eJ)(l.j,e,t),a={uid:s.uid,email:s.email,role:"user",displayName:s.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(l.db,"users",s.uid),a)}catch(e){throw console.error("Sign up error:",e),e}},g=async()=>{await (0,i.CI)(l.j)},p=async e=>{if(!s)throw Error("No user logged in");try{let t={...e,updatedAt:new Date};await (0,n.BN)((0,n.H9)(l.db,"users",s.uid),t,{merge:!0}),c&&x({...c,...t})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,a.jsx)(o.Provider,{value:{user:s,userData:c,loading:m,signIn:u,signUp:f,logout:g,updateUserProfile:p},children:t})}},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(9708),i=s(2085),n=s(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:i,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:i,className:t})),...d})}},347:()=>{},1466:(e,t,s)=>{"use strict";s.d(t,{Navbar:()=>O});var a=s(5155),r=s(2115),i=s(6874),n=s.n(i),l=s(283),o=s(285),d=s(8698),c=s(9434);function x(e){let{...t}=e;return(0,a.jsx)(d.bL,{"data-slot":"dropdown-menu",...t})}function m(e){let{...t}=e;return(0,a.jsx)(d.l9,{"data-slot":"dropdown-menu-trigger",...t})}function h(e){let{className:t,sideOffset:s=4,...r}=e;return(0,a.jsx)(d.ZL,{children:(0,a.jsx)(d.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function u(e){let{className:t,inset:s,variant:r="default",...i}=e;return(0,a.jsx)(d.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":r,className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(d.wv,{"data-slot":"dropdown-menu-separator",className:(0,c.cn)("bg-border -mx-1 my-1 h-px",t),...s})}var g=s(4011);function p(e){let{className:t,...s}=e;return(0,a.jsx)(g.bL,{"data-slot":"avatar",className:(0,c.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...s})}function v(e){let{className:t,...s}=e;return(0,a.jsx)(g.H4,{"data-slot":"avatar-fallback",className:(0,c.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...s})}var j=s(5452),b=s(4416);function y(e){let{...t}=e;return(0,a.jsx)(j.bL,{"data-slot":"sheet",...t})}function N(e){let{...t}=e;return(0,a.jsx)(j.l9,{"data-slot":"sheet-trigger",...t})}function w(e){let{...t}=e;return(0,a.jsx)(j.ZL,{"data-slot":"sheet-portal",...t})}function k(e){let{className:t,...s}=e;return(0,a.jsx)(j.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function A(e){let{className:t,children:s,side:r="right",...i}=e;return(0,a.jsxs)(w,{children:[(0,a.jsx)(k,{}),(0,a.jsxs)(j.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...i,children:[s,(0,a.jsxs)(j.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(b.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var C=s(7340),S=s(7434),z=s(3127),P=s(9420),E=s(1007),_=s(381),I=s(6151),L=s(4835),D=s(4783),H=s(4616);let O=()=>{var e,t,s;let{user:i,userData:d,logout:c}=(0,l.A)(),[g,j]=(0,r.useState)(!1),b=async()=>{try{await c()}catch(e){console.error("Error logging out:",e)}};return(0,a.jsx)("nav",{className:"border-b bg-white shadow-sm sticky top-0 z-50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex h-14 sm:h-16 items-center justify-between",children:[(0,a.jsx)(n(),{href:"/",className:"flex items-center",children:(0,a.jsx)("span",{className:"font-bold text-lg sm:text-xl text-gray-900",children:"KaleidoneX"})}),(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-6 xl:space-x-8",children:(0,a.jsx)(()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n(),{href:"/",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center",children:[(0,a.jsx)(C.A,{className:"w-4 h-4 mr-2"}),"Home"]}),(0,a.jsxs)(n(),{href:"/templates",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center",children:[(0,a.jsx)(S.A,{className:"w-4 h-4 mr-2"}),"Templates"]}),(0,a.jsxs)(n(),{href:"/custom-request",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center",children:[(0,a.jsx)(z.A,{className:"w-4 h-4 mr-2"}),"Customize"]}),(0,a.jsxs)(n(),{href:"/contact",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center",children:[(0,a.jsx)(P.A,{className:"w-4 h-4 mr-2"}),"Contact"]})]}),{})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[i?(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(x,{children:[(0,a.jsx)(m,{asChild:!0,children:(0,a.jsx)(o.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsx)(p,{className:"h-8 w-8",children:(0,a.jsx)(v,{children:(null==d||null==(e=d.displayName)?void 0:e[0])||(null==(s=i.email)||null==(t=s[0])?void 0:t.toUpperCase())})})})}),(0,a.jsxs)(h,{className:"w-56",align:"end",forceMount:!0,children:[(0,a.jsx)(u,{asChild:!0,children:(0,a.jsxs)(n(),{href:(null==d?void 0:d.role)==="admin"?"/admin":"/dashboard",className:"flex items-center",children:[(0,a.jsx)(E.A,{className:"mr-2 h-4 w-4"}),(null==d?void 0:d.role)==="admin"?"Admin Dashboard":"Dashboard"]})}),(0,a.jsx)(u,{asChild:!0,children:(0,a.jsxs)(n(),{href:"/profile",className:"flex items-center",children:[(0,a.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Profile Settings"]})}),(0,a.jsx)(u,{asChild:!0,children:(0,a.jsxs)(n(),{href:"/orders",className:"flex items-center",children:[(0,a.jsx)(I.A,{className:"mr-2 h-4 w-4"}),"My Orders"]})}),(0,a.jsx)(f,{}),(0,a.jsxs)(u,{onClick:b,children:[(0,a.jsx)(L.A,{className:"mr-2 h-4 w-4"}),"Log out"]})]})]})}):(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-2",children:(0,a.jsx)(o.$,{asChild:!0,variant:"ghost",className:"text-gray-700 hover:text-gray-900 text-sm",children:(0,a.jsxs)(n(),{href:"/auth",className:"flex items-center",children:[(0,a.jsx)(E.A,{className:"w-4 h-4 mr-2"}),"Sign In"]})})}),(0,a.jsxs)(y,{open:g,onOpenChange:j,children:[(0,a.jsx)(N,{asChild:!0,className:"lg:hidden",children:(0,a.jsx)(o.$,{variant:"ghost",size:"sm",className:"p-2",children:(0,a.jsx)(D.A,{className:"h-5 w-5"})})}),(0,a.jsx)(A,{side:"right",className:"w-[280px] sm:w-[350px]",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-4 mt-6",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,a.jsxs)(n(),{href:"/",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>j(!1),children:[(0,a.jsx)(C.A,{className:"w-4 h-4 mr-3"}),"Home"]}),(0,a.jsxs)(n(),{href:"/templates",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>j(!1),children:[(0,a.jsx)(S.A,{className:"w-4 h-4 mr-3"}),"Templates"]}),(0,a.jsxs)(n(),{href:"/custom-request",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>j(!1),children:[(0,a.jsx)(z.A,{className:"w-4 h-4 mr-3"}),"Customize"]}),(0,a.jsxs)(n(),{href:"/contact",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>j(!1),children:[(0,a.jsx)(P.A,{className:"w-4 h-4 mr-3"}),"Contact"]})]}),i?(0,a.jsx)("div",{className:"flex flex-col space-y-3 pt-4 border-t",children:(0,a.jsxs)("button",{onClick:()=>{c(),j(!1)},className:"text-left text-sm font-medium text-red-600 hover:text-red-700 transition-colors flex items-center py-2",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 mr-3"}),"Sign Out"]})}):(0,a.jsxs)("div",{className:"flex flex-col space-y-3 pt-4 border-t",children:[(0,a.jsxs)(n(),{href:"/auth",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>j(!1),children:[(0,a.jsx)(E.A,{className:"w-4 h-4 mr-3"}),"Sign In"]}),(0,a.jsxs)(n(),{href:"/auth?mode=signup",className:"text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors flex items-center py-2",onClick:()=>j(!1),children:[(0,a.jsx)(H.A,{className:"w-4 h-4 mr-3"}),"Get Started"]})]})]})})]})]})]})})})}},1973:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4147,23)),Promise.resolve().then(s.t.bind(s,8489,23)),Promise.resolve().then(s.bind(s,6671)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,6821)),Promise.resolve().then(s.bind(s,1466)),Promise.resolve().then(s.bind(s,283)),Promise.resolve().then(s.bind(s,7740))},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,type:s,...i}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>o,j:()=>l});var a=s(3915),r=s(6203),i=s(5317);let n=(0,a.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),l=(0,r.xI)(n),o=(0,i.aU)(n)},6821:(e,t,s)=>{"use strict";s.d(t,{Footer:()=>p});var a=s(5155);s(2115);var r=s(6874),i=s.n(r),n=s(285),l=s(2523),o=s(488),d=s(8175),c=s(5684),x=s(2894),m=s(9099),h=s(8883),u=s(9420),f=s(4516),g=s(1976);let p=()=>(0,a.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-bold",children:"KaleidoneX"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Premium templates and digital solutions for modern businesses. Create stunning websites with our professionally designed templates."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(o.A,{className:"h-5 w-5"})}),(0,a.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(d.A,{className:"h-5 w-5"})}),(0,a.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(c.A,{className:"h-5 w-5"})}),(0,a.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(x.A,{className:"h-5 w-5"})}),(0,a.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(m.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/",className:"text-gray-400 hover:text-white transition-colors",children:"Home"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/templates",className:"text-gray-400 hover:text-white transition-colors",children:"Templates"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/categories",className:"text-gray-400 hover:text-white transition-colors",children:"Categories"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/about",className:"text-gray-400 hover:text-white transition-colors",children:"About Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/contact",className:"text-gray-400 hover:text-white transition-colors",children:"Contact"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold",children:"Support"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/help",className:"text-gray-400 hover:text-white transition-colors",children:"Help Center"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/documentation",className:"text-gray-400 hover:text-white transition-colors",children:"Documentation"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/faq",className:"text-gray-400 hover:text-white transition-colors",children:"FAQ"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/privacy",className:"text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/terms",className:"text-gray-400 hover:text-white transition-colors",children:"Terms of Service"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold",children:"Stay Updated"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Subscribe to our newsletter for the latest templates and updates."}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.p,{type:"email",placeholder:"Enter your email",className:"bg-gray-800 border-gray-700 text-white placeholder-gray-400"}),(0,a.jsx)(n.$,{className:"w-full bg-blue-600 hover:bg-blue-700",children:"Subscribe"})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-800 pt-8 mb-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"+1 (555) 123-4567"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"San Francisco, CA"})]})]})}),(0,a.jsx)("div",{className:"border-t border-gray-800 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"\xa9 2024 KaleidoneX. All rights reserved."}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-400",children:[(0,a.jsx)("span",{children:"Made with"}),(0,a.jsx)(g.A,{className:"h-4 w-4 text-red-500 fill-current"}),(0,a.jsx)("span",{children:"by KaleidoneX Team"})]})]})})]})})},7740:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>n});var a=s(5155),r=s(2115);let i=(0,r.createContext)(void 0),n=e=>{let{children:t}=e,[s,n]=(0,r.useState)("light");return(0,r.useEffect)(()=>{let e=localStorage.getItem("theme");e?n(e):n(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")},[]),(0,r.useEffect)(()=>{let e=document.documentElement;"dark"===s?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("theme",s)},[s]),(0,a.jsx)(i.Provider,{value:{theme:s,toggleTheme:()=>{n(e=>"light"===e?"dark":"light")}},children:t})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(2596),r=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[896,992,965,455,645,874,942,242,808,758,107,441,684,358],()=>t(1973)),_N_E=e.O()}]);