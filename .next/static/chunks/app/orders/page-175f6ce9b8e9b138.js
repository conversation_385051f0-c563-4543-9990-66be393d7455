(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>c,AuthProvider:()=>o});var a=s(5155),r=s(2115),i=s(6203),d=s(5317),l=s(6104);let n=(0,r.createContext)(void 0),c=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},o=e=>{let{children:t}=e,[s,c]=(0,r.useState)(null),[o,u]=(0,r.useState)(null),[m,x]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(l.j,async e=>{if(e){c(e);try{let t=await (0,d.x7)((0,d.H9)(l.db,"users",e.uid));if(t.exists())u(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,d.BN)((0,d.H9)(l.db,"users",e.uid),t),u(t)}}catch(t){console.error("Error fetching/creating user data:",t),u({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else c(null),u(null);x(!1)});return()=>e()},[]);let h=async(e,t)=>{try{await (0,i.x9)(l.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},p=async(e,t)=>{try{let{user:s}=await (0,i.eJ)(l.j,e,t),a={uid:s.uid,email:s.email,role:"user",displayName:s.displayName||"",createdAt:new Date};await (0,d.BN)((0,d.H9)(l.db,"users",s.uid),a)}catch(e){throw console.error("Sign up error:",e),e}},g=async()=>{await (0,i.CI)(l.j)},v=async e=>{if(!s)throw Error("No user logged in");try{let t={...e,updatedAt:new Date};await (0,d.BN)((0,d.H9)(l.db,"users",s.uid),t,{merge:!0}),o&&u({...o,...t})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,a.jsx)(n.Provider,{value:{user:s,userData:o,loading:m,signIn:h,signUp:p,logout:g,updateUserProfile:v},children:t})}},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var a=s(5155);s(2115);var r=s(9708),i=s(2085),d=s(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function n(e){let{className:t,variant:s,size:i,asChild:n=!1,...c}=e,o=n?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,d.cn)(l({variant:s,size:i,className:t})),...c})}},646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1788:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5804:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(5155);s(2115);var r=s(283),i=s(285),d=s(6695),l=s(6126),n=s(646),c=s(4186),o=s(4861),u=s(6151),m=s(5868),x=s(9074),h=s(2657),p=s(1788),g=s(6874),v=s.n(g);let f=[{id:"ORD-001",templateId:"1",templateName:"Modern Dashboard Pro",templateImage:"/api/placeholder/100/80",amount:49,status:"completed",orderDate:"2024-01-15",downloadCount:3,maxDownloads:5},{id:"ORD-002",templateId:"2",templateName:"E-commerce Store Complete",templateImage:"/api/placeholder/100/80",amount:79,status:"confirmed",orderDate:"2024-01-10",downloadCount:1,maxDownloads:3},{id:"ORD-003",templateId:"3",templateName:"Landing Page Pro",templateImage:"/api/placeholder/100/80",amount:39,status:"pending",orderDate:"2024-01-08",downloadCount:0,maxDownloads:5},{id:"ORD-004",templateId:"4",templateName:"Creative Portfolio",templateImage:"/api/placeholder/100/80",amount:29,status:"declined",orderDate:"2024-01-05",downloadCount:0,maxDownloads:3}],y=e=>{switch(e){case"completed":return(0,a.jsx)(n.A,{className:"h-5 w-5 text-green-500"});case"confirmed":return(0,a.jsx)(n.A,{className:"h-5 w-5 text-blue-500"});case"pending":return(0,a.jsx)(c.A,{className:"h-5 w-5 text-yellow-500"});case"declined":return(0,a.jsx)(o.A,{className:"h-5 w-5 text-red-500"});default:return(0,a.jsx)(c.A,{className:"h-5 w-5 text-gray-500"})}},b=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"confirmed":return"bg-blue-100 text-blue-800";case"pending":return"bg-yellow-100 text-yellow-800";case"declined":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};function j(){let{user:e}=(0,r.A)();if(!e)return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Please sign in to view your orders"}),(0,a.jsx)(i.$,{asChild:!0,children:(0,a.jsx)(v(),{href:"/auth",children:"Sign In"})})]});let t=f.filter(e=>"completed"===e.status).length,s=f.filter(e=>"completed"===e.status||"confirmed"===e.status).reduce((e,t)=>e+t.amount,0);return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"My Orders"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track your template purchases and downloads"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:f.length})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-blue-600"})})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)(n.A,{className:"h-6 w-6 text-green-600"})})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Spent"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["$",s]})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,a.jsx)(m.A,{className:"h-6 w-6 text-purple-600"})})]})})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"Order History"}),(0,a.jsx)(d.BT,{children:"All your template purchases and their current status"})]}),(0,a.jsx)(d.Wu,{children:0===f.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No orders yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Start browsing our premium templates to make your first purchase."}),(0,a.jsx)(i.$,{asChild:!0,children:(0,a.jsx)(v(),{href:"/templates",children:"Browse Templates"})})]}):(0,a.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-6 hover:shadow-md transition-shadow",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,a.jsx)("div",{className:"w-20 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0",children:(0,a.jsx)("img",{src:e.templateImage,alt:e.templateName,className:"w-full h-full object-cover"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:e.templateName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Order #",e.id]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:["$",e.amount]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,a.jsx)(x.A,{className:"h-3 w-3 mr-1"}),new Date(e.orderDate).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[y(e.status),(0,a.jsx)(l.E,{className:b(e.status),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),("completed"===e.status||"confirmed"===e.status)&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Downloads: ",e.downloadCount,"/",e.maxDownloads]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.$,{size:"sm",variant:"outline",asChild:!0,children:(0,a.jsxs)(v(),{href:"/templates/".concat(e.templateId),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"View"]})}),"completed"===e.status&&(0,a.jsxs)(i.$,{size:"sm",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Download"]})]})]})]})]})})},e.id))})})]}),(0,a.jsx)(d.Zp,{className:"mt-8",children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Need Help with Your Order?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"If you have any questions about your orders or need assistance with downloads, we're here to help."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(i.$,{asChild:!0,variant:"outline",children:(0,a.jsx)(v(),{href:"/contact",children:"Contact Support"})}),(0,a.jsx)(i.$,{asChild:!0,variant:"outline",children:(0,a.jsx)(v(),{href:"/templates",children:"Browse More Templates"})})]})]})})})]})}},5868:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>n,j:()=>l});var a=s(3915),r=s(6203),i=s(5317);let d=(0,a.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),l=(0,r.xI)(d),n=(0,i.aU)(d)},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(5155);s(2115);var r=s(9708),i=s(2085),d=s(9434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:s,asChild:i=!1,...n}=e,c=i?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,d.cn)(l({variant:s}),t),...n})}},6151:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>n,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>d});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},8398:(e,t,s)=>{Promise.resolve().then(s.bind(s,5804))},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(2596),r=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,455,645,874,441,684,358],()=>t(8398)),_N_E=e.O()}]);