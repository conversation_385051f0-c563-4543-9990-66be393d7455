(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[363],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>c});var r=a(5155),s=a(2115),i=a(6203),l=a(5317),n=a(6104);let d=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[a,o]=(0,s.useState)(null),[c,u]=(0,s.useState)(null),[p,x]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=(0,i.hg)(n.j,async e=>{if(e){o(e);try{let t=await (0,l.x7)((0,l.H9)(n.db,"users",e.uid));if(t.exists())u(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,l.BN)((0,l.H9)(n.db,"users",e.uid),t),u(t)}}catch(t){console.error("Error fetching/creating user data:",t),u({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else o(null),u(null);x(!1)});return()=>e()},[]);let h=async(e,t)=>{try{await (0,i.x9)(n.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},m=async(e,t)=>{try{let{user:a}=await (0,i.eJ)(n.j,e,t),r={uid:a.uid,email:a.email,role:"user",displayName:a.displayName||"",createdAt:new Date};await (0,l.BN)((0,l.H9)(n.db,"users",a.uid),r)}catch(e){throw console.error("Sign up error:",e),e}},g=async()=>{await (0,i.CI)(n.j)},v=async e=>{if(!a)throw Error("No user logged in");try{let t={...e,updatedAt:new Date};await (0,l.BN)((0,l.H9)(n.db,"users",a.uid),t,{merge:!0}),c&&u({...c,...t})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,r.jsx)(d.Provider,{value:{user:a,userData:c,loading:p,signIn:h,signUp:m,logout:g,updateUserProfile:v},children:t})}},285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var r=a(5155);a(2115);var s=a(9708),i=a(2085),l=a(9434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:i,asChild:d=!1,...o}=e,c=d?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:a,size:i,className:t})),...o})}},968:(e,t,a)=>{"use strict";a.d(t,{b:()=>n});var r=a(2115),s=a(3655),i=a(5155),l=r.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var r=a(5155);a(2115);var s=a(9434);function i(e){let{className:t,type:a,...i}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},4616:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4719:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var r=a(5155),s=a(2115),i=a(283),l=a(6695),n=a(285),d=a(2523),o=a(5057),c=a(8539),u=a(9409);let p=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var x=a(4616),h=a(6874),m=a.n(h);function g(){let{user:e,userData:t}=(0,i.A)(),[a,h]=(0,s.useState)(!1),[g,v]=(0,s.useState)({title:"",description:"",category:"",price:"",features:"",demoUrl:"",downloadUrl:""}),f=async e=>{e.preventDefault(),h(!0);try{console.log("Template data:",g),alert("Template added successfully!"),v({title:"",description:"",category:"",price:"",features:"",demoUrl:"",downloadUrl:""})}catch(e){console.error("Error adding template:",e),alert("Failed to add template")}finally{h(!1)}},b=(e,t)=>{v(a=>({...a,[e]:t}))};return e&&(null==t?void 0:t.role)==="admin"?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4 mb-4",children:(0,r.jsx)(n.$,{asChild:!0,variant:"outline",size:"sm",children:(0,r.jsxs)(m(),{href:"/admin",children:[(0,r.jsx)(p,{className:"mr-2 h-4 w-4"}),"Back to Dashboard"]})})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Add New Template"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Add a new template to your marketplace"})]}),(0,r.jsx)("div",{className:"max-w-2xl",children:(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"mr-2 h-5 w-5"}),"Template Details"]}),(0,r.jsx)(l.BT,{children:"Fill in the information for your new template"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"title",children:"Template Title"}),(0,r.jsx)(d.p,{id:"title",value:g.title,onChange:e=>b("title",e.target.value),placeholder:"e.g., SaaS Dashboard Pro",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"category",children:"Category"}),(0,r.jsxs)(u.l6,{value:g.category,onValueChange:e=>b("category",e),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select category"})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:"technology",children:"Technology"}),(0,r.jsx)(u.eb,{value:"business",children:"Business"}),(0,r.jsx)(u.eb,{value:"education",children:"Education"}),(0,r.jsx)(u.eb,{value:"portfolio",children:"Portfolio"}),(0,r.jsx)(u.eb,{value:"ecommerce",children:"E-commerce"}),(0,r.jsx)(u.eb,{value:"blog",children:"Blog"})]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"description",children:"Description"}),(0,r.jsx)(c.T,{id:"description",value:g.description,onChange:e=>b("description",e.target.value),placeholder:"Describe your template...",rows:3,required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"price",children:"Price (₹)"}),(0,r.jsx)(d.p,{id:"price",type:"number",value:g.price,onChange:e=>b("price",e.target.value),placeholder:"2499",min:"0",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"demoUrl",children:"Demo URL"}),(0,r.jsx)(d.p,{id:"demoUrl",type:"url",value:g.demoUrl,onChange:e=>b("demoUrl",e.target.value),placeholder:"https://demo.example.com"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"features",children:"Features (one per line)"}),(0,r.jsx)(c.T,{id:"features",value:g.features,onChange:e=>b("features",e.target.value),placeholder:"Responsive Design Dark Mode Support Admin Dashboard User Authentication",rows:4})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"downloadUrl",children:"Download URL"}),(0,r.jsx)(d.p,{id:"downloadUrl",type:"url",value:g.downloadUrl,onChange:e=>b("downloadUrl",e.target.value),placeholder:"https://files.example.com/template.zip"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",asChild:!0,children:(0,r.jsx)(m(),{href:"/admin",children:"Cancel"})}),(0,r.jsx)(n.$,{type:"submit",disabled:a,children:a?"Adding...":"Add Template"})]})]})})]})})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You need admin privileges to access this page."}),(0,r.jsx)(n.$,{asChild:!0,children:(0,r.jsx)(m(),{href:"/dashboard",children:"Go to Dashboard"})})]})}},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(5155);a(2115);var s=a(968),i=a(9434);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>d,j:()=>n});var r=a(3915),s=a(6203),i=a(5317);let l=(0,r.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),n=(0,s.xI)(l),d=(0,i.aU)(l)},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>l});var r=a(5155);a(2115);var s=a(9434);function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},7675:(e,t,a)=>{Promise.resolve().then(a.bind(a,4719))},8539:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var r=a(5155);a(2115);var s=a(9434);function i(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},9409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>x,gC:()=>p,l6:()=>o,yv:()=>c});var r=a(5155);a(2115);var s=a(8715),i=a(6474),l=a(5196),n=a(7863),d=a(9434);function o(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:l,...n}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[l,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:a,position:i="popper",...l}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...l,children:[(0,r.jsx)(h,{}),(0,r.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(m,{})]})})}function x(e){let{className:t,children:a,...i}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(l.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(i.A,{className:"size-4"})})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var r=a(2596),s=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,455,645,874,942,242,171,441,684,358],()=>t(7675)),_N_E=e.O()}]);