(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[912],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>l,AuthProvider:()=>c});var s=a(5155),r=a(2115),i=a(6203),n=a(5317),o=a(6104);let d=(0,r.createContext)(void 0),l=()=>{let e=(0,r.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[a,l]=(0,r.useState)(null),[c,m]=(0,r.useState)(null),[p,u]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(o.j,async e=>{if(e){l(e);try{let t=await (0,n.x7)((0,n.H9)(o.db,"users",e.uid));if(t.exists())m(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(o.db,"users",e.uid),t),m(t)}}catch(t){console.error("Error fetching/creating user data:",t),m({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else l(null),m(null);u(!1)});return()=>e()},[]);let h=async(e,t)=>{try{await (0,i.x9)(o.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},g=async(e,t)=>{try{let{user:a}=await (0,i.eJ)(o.j,e,t),s={uid:a.uid,email:a.email,role:"user",displayName:a.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(o.db,"users",a.uid),s)}catch(e){throw console.error("Sign up error:",e),e}},f=async()=>{await (0,i.CI)(o.j)},x=async e=>{if(!a)throw Error("No user logged in");try{let t={...e,updatedAt:new Date};await (0,n.BN)((0,n.H9)(o.db,"users",a.uid),t,{merge:!0}),c&&m({...c,...t})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,s.jsx)(d.Provider,{value:{user:a,userData:c,loading:p,signIn:h,signUp:g,logout:f,updateUserProfile:x},children:t})}},285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var s=a(5155);a(2115);var r=a(9708),i=a(2085),n=a(9434);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:i,asChild:d=!1,...l}=e,c=d?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:a,size:i,className:t})),...l})}},646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1706:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(5155),r=a(2115),i=a(285),n=a(6695),o=a(5365),d=a(4213),l=a(646),c=a(1154),m=a(7580),p=a(5317),u=a(6104);let h=[{title:"Modern Dashboard",description:"Clean and modern dashboard template with analytics and data visualization",category:"Dashboard",price:49,imageUrl:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["React","TypeScript","Charts","Analytics"],featured:!0,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"E-commerce Store",description:"Complete e-commerce solution with shopping cart and payment integration",category:"E-commerce",price:79,imageUrl:"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["Next.js","Stripe","Shopping Cart","Responsive"],featured:!0,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"Landing Page Pro",description:"High-converting landing page template for SaaS and startups",category:"Landing Page",price:39,imageUrl:"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["HTML","CSS","JavaScript","Conversion"],featured:!0,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"Portfolio Showcase",description:"Creative portfolio template for designers and developers",category:"Portfolio",price:29,imageUrl:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["Vue.js","GSAP","Animation","Creative"],featured:!1,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"Corporate Website",description:"Professional corporate website template with multiple pages",category:"Corporate",price:59,imageUrl:"https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["WordPress","PHP","Corporate","Professional"],featured:!1,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"Mobile App UI",description:"Complete mobile app UI kit with 50+ screens",category:"Mobile App",price:69,imageUrl:"https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["React Native","Flutter","Mobile","UI Kit"],featured:!1,createdAt:new Date,updatedAt:new Date,createdBy:"admin"}],g=[{name:"Dashboard",description:"Admin panels and data visualization templates",imageUrl:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",templateCount:0},{name:"E-commerce",description:"Online stores and shopping cart templates",imageUrl:"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",templateCount:0},{name:"Landing Page",description:"High-converting marketing pages",imageUrl:"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",templateCount:0},{name:"Portfolio",description:"Creative showcases for professionals",imageUrl:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop",templateCount:0},{name:"Corporate",description:"Business and company websites",imageUrl:"https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop",templateCount:0},{name:"Mobile App",description:"Mobile application UI templates",imageUrl:"https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop",templateCount:0}],f=async()=>{try{for(let e of(console.log("Adding sample templates..."),h))await (0,p.gS)((0,p.rJ)(u.db,"templates"),e);for(let e of(console.log("Adding sample categories..."),g))await (0,p.gS)((0,p.rJ)(u.db,"categories"),e);for(let e of(console.log("Adding sample custom requests..."),x))await (0,p.gS)((0,p.rJ)(u.db,"customRequests"),e);for(let e of(console.log("Adding sample users..."),w)){let t=(0,p.H9)((0,p.rJ)(u.db,"users"));await (0,p.BN)(t,{...e,uid:t.id})}for(let e of(console.log("Adding sample contact messages..."),v))await (0,p.gS)((0,p.rJ)(u.db,"contactMessages"),e);console.log("All sample data added successfully!")}catch(e){throw console.error("Error adding sample data:",e),e}},x=[{userId:"user1",userEmail:"<EMAIL>",title:"Modern SaaS Dashboard",description:"I need a modern dashboard for my SaaS application with analytics, user management, and billing features.",category:"Dashboard",budget:800,deadline:new Date("2024-02-15"),status:"pending",createdAt:new Date("2024-01-10"),updatedAt:new Date("2024-01-10")},{userId:"user2",userEmail:"<EMAIL>",title:"E-commerce Mobile App",description:"Looking for a mobile-first e-commerce template with product catalog, shopping cart, and payment integration.",category:"E-commerce",budget:1200,deadline:new Date("2024-02-20"),status:"in-progress",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-12"),adminNotes:"Started working on wireframes and design mockups."},{userId:"user3",userEmail:"<EMAIL>",title:"Portfolio Website",description:"Creative portfolio website for a photographer with gallery, blog, and contact features.",category:"Portfolio",budget:500,status:"completed",createdAt:new Date("2024-01-05"),updatedAt:new Date("2024-01-15"),adminNotes:"Completed and delivered. Client very satisfied."},{userId:"user4",userEmail:"<EMAIL>",title:"Corporate Landing Page",description:"Professional landing page for a consulting firm with services showcase and lead generation forms.",category:"Landing Page",budget:600,deadline:new Date("2024-02-10"),status:"pending",createdAt:new Date("2024-01-12"),updatedAt:new Date("2024-01-12")},{userId:"user5",userEmail:"<EMAIL>",title:"Restaurant Website",description:"Website for a restaurant with menu display, online ordering, and reservation system.",category:"Other",budget:900,status:"cancelled",createdAt:new Date("2024-01-03"),updatedAt:new Date("2024-01-07"),adminNotes:"Client cancelled due to budget constraints."}],w=[{email:"<EMAIL>",role:"user",fullName:"John Doe",displayName:"John",phoneNumber:"1234567890",countryCode:"+1",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-10")},{email:"<EMAIL>",role:"user",fullName:"Sarah Smith",displayName:"Sarah",phoneNumber:"9876543210",countryCode:"+1",createdAt:new Date("2024-01-02"),updatedAt:new Date("2024-01-08")},{email:"<EMAIL>",role:"user",fullName:"Mike Johnson",displayName:"Mike",phoneNumber:"5555555555",countryCode:"+44",createdAt:new Date("2024-01-03"),updatedAt:new Date("2024-01-05")},{email:"<EMAIL>",role:"user",fullName:"Lisa Brown",displayName:"Lisa",phoneNumber:"7777777777",countryCode:"+91",createdAt:new Date("2024-01-04"),updatedAt:new Date("2024-01-12")},{email:"<EMAIL>",role:"user",fullName:"David Wilson",displayName:"David",phoneNumber:"3333333333",countryCode:"+61",createdAt:new Date("2024-01-05"),updatedAt:new Date("2024-01-07")}],v=[{name:"Alex Thompson",email:"<EMAIL>",subject:"Question about custom development",message:"Hi, I'm interested in getting a custom template developed. Can you provide more information about your process and pricing?",status:"unread",createdAt:new Date("2024-01-14")},{name:"Emma Davis",email:"<EMAIL>",subject:"Template customization request",message:"I purchased the Modern Dashboard template and would like to customize the color scheme. Is this service available?",status:"read",createdAt:new Date("2024-01-13")},{name:"Ryan Miller",email:"<EMAIL>",subject:"Technical support needed",message:"I'm having trouble setting up the e-commerce template. The payment integration is not working as expected.",status:"replied",createdAt:new Date("2024-01-12")}];var y=a(283);function b(){let[e,t]=(0,r.useState)(!1),[a,p]=(0,r.useState)(!1),[u,h]=(0,r.useState)(""),[g,x]=(0,r.useState)(!1),[w,v]=(0,r.useState)(!1),[b,j]=(0,r.useState)(""),[A,N]=(0,r.useState)(""),{user:D}=(0,y.A)(),k=async()=>{t(!0),h(""),p(!1);try{await f(),p(!0)}catch(e){h(e.message||"Failed to add sample data")}finally{t(!1)}};return(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Setup"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Set up your KaleidoneX marketplace with sample data"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"Sample Data"]}),(0,s.jsx)(n.BT,{children:"Add sample templates and categories to get started quickly"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["This will add:",(0,s.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,s.jsx)("li",{children:"6 sample templates across different categories"}),(0,s.jsx)("li",{children:"6 template categories"}),(0,s.jsx)("li",{children:"Professional images from Unsplash"})]})]}),u&&(0,s.jsx)(o.Fc,{variant:"destructive",children:(0,s.jsx)(o.TN,{children:u})}),a&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Sample data added successfully! You can now browse templates on your site."})]}),(0,s.jsxs)(i.$,{onClick:k,disabled:e||a,className:"w-full",children:[e&&(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),a?"Sample Data Added":"Add Sample Data"]})]})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Next Steps"]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4 text-sm text-gray-600",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"1. Make yourself an admin"}),(0,s.jsx)("p",{children:'Go to Firebase Console → Firestore → users collection → find your user → change role to "admin"'})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"2. Test the application"}),(0,s.jsx)("p",{children:"Browse templates, test the search and filtering functionality"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"3. Customize templates"}),(0,s.jsx)("p",{children:"Replace sample templates with your own designs and content"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"4. Set up payments"}),(0,s.jsx)("p",{children:"Integrate Stripe or your preferred payment processor"})]})]})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"Firebase Configuration"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Authentication"}),(0,s.jsx)("span",{className:"text-green-600 font-medium",children:"✓ Configured"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Firestore Database"}),(0,s.jsx)("span",{className:"text-green-600 font-medium",children:"✓ Connected"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Security Rules"}),(0,s.jsx)("span",{className:"text-yellow-600 font-medium",children:"⚠ Check manually"})]})]})})]})]})]})})}},4213:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},5365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>o,TN:()=>d});var s=a(5155);a(2115);var r=a(2085),i=a(9434);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:a}),t),...r})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>d,j:()=>o});var s=a(3915),r=a(6203),i=a(5317);let n=(0,s.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),o=(0,r.xI)(n),d=(0,i.aU)(n)},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=a(5155);a(2115);var r=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},7580:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8742:(e,t,a)=>{Promise.resolve().then(a.bind(a,1706))},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var s=a(2596),r=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,455,645,441,684,358],()=>t(8742)),_N_E=e.O()}]);