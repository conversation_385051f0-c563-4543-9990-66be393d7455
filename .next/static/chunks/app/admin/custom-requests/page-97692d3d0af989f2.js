(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,AuthProvider:()=>o});var a=s(5155),r=s(2115),i=s(6203),n=s(5317),d=s(6104);let c=(0,r.createContext)(void 0),l=()=>{let e=(0,r.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},o=e=>{let{children:t}=e,[s,l]=(0,r.useState)(null),[o,u]=(0,r.useState)(null),[x,m]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(d.j,async e=>{if(e){l(e);try{let t=await (0,n.x7)((0,n.H9)(d.db,"users",e.uid));if(t.exists())u(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",e.uid),t),u(t)}}catch(t){console.error("Error fetching/creating user data:",t),u({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else l(null),u(null);m(!1)});return()=>e()},[]);let h=async(e,t)=>{try{await (0,i.x9)(d.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},p=async(e,t)=>{try{let{user:s}=await (0,i.eJ)(d.j,e,t),a={uid:s.uid,email:s.email,role:"user",displayName:s.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",s.uid),a)}catch(e){throw console.error("Sign up error:",e),e}},g=async()=>{await (0,i.CI)(d.j)},v=async e=>{if(!s)throw Error("No user logged in");try{let t={...e,updatedAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",s.uid),t,{merge:!0}),o&&u({...o,...t})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,a.jsx)(c.Provider,{value:{user:s,userData:o,loading:x,signIn:h,signUp:p,logout:g,updateUserProfile:v},children:t})}},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(5155);s(2115);var r=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:i,asChild:c=!1,...l}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:s,size:i,className:t})),...l})}},646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},968:(e,t,s)=>{"use strict";s.d(t,{b:()=>d});var a=s(2115),r=s(3655),i=s(5155),n=a.forwardRef((e,t)=>(0,i.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var d=n},1007:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1497:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},2288:(e,t,s)=>{"use strict";s.d(t,{CF:()=>c,CV:()=>u,FQ:()=>m,KV:()=>n,SZ:()=>h,Tb:()=>x,Xm:()=>o,gW:()=>d,hx:()=>i,wx:()=>l});var a=s(5317),r=s(6104);let i=async e=>{try{return(await (0,a.gS)((0,a.rJ)(r.db,"customRequests"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating custom request:",e),e}},n=async()=>{try{let e=(0,a.P)((0,a.rJ)(r.db,"customRequests"),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching custom requests:",e),e}},d=async(e,t,s)=>{try{let i={status:t,updatedAt:new Date};s&&(i.adminNotes=s),await (0,a.mZ)((0,a.H9)(r.db,"customRequests",e),i)}catch(e){throw console.error("Error updating custom request:",e),e}},c=async()=>{try{let e=(0,a.P)((0,a.rJ)(r.db,"users"),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching users:",e),e}},l=async()=>{try{let[e,t,s]=await Promise.all([(0,a.GG)((0,a.rJ)(r.db,"users")),(0,a.GG)((0,a.rJ)(r.db,"templates")),(0,a.GG)((0,a.rJ)(r.db,"customRequests"))]),i=e.size,n=t.size,d=s.size,c=s.docs.filter(e=>"pending"===e.data().status).length;return{totalUsers:i,totalTemplates:n,totalRequests:d,pendingRequests:c,totalSales:0,customizations:0}}catch(e){throw console.error("Error fetching dashboard stats:",e),e}},o=e=>{let t=(0,a.P)((0,a.rJ)(r.db,"customRequests"),(0,a.My)("createdAt","desc"));return(0,a.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})},u=async e=>{try{return(await (0,a.gS)((0,a.rJ)(r.db,"contactMessages"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating contact message:",e),e}},x=async()=>{try{let e=(0,a.P)((0,a.rJ)(r.db,"contactMessages"),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching contact messages:",e),e}},m=async(e,t,s)=>{try{let i={status:t,updatedAt:new Date};s&&(i.adminNotes=s),await (0,a.mZ)((0,a.H9)(r.db,"contactMessages",e),i)}catch(e){throw console.error("Error updating contact message:",e),e}},h=e=>{let t=(0,a.P)((0,a.rJ)(r.db,"contactMessages"),(0,a.My)("createdAt","desc"));return(0,a.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})}},2501:(e,t,s)=>{Promise.resolve().then(s.bind(s,3175))},3175:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var a=s(5155),r=s(2115),i=s(283),n=s(6695),d=s(285),c=s(6126),l=s(8539),o=s(5057),u=s(5365),x=s(4186);let m=(0,s(9946).A)("circle-play",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]]);var h=s(646),p=s(4861),g=s(7434),v=s(1007),b=s(9074),f=s(5868),y=s(1497),j=s(1154),N=s(6874),w=s.n(N),A=s(2288);function k(){let{user:e,userData:t}=(0,i.A)(),[s,N]=(0,r.useState)([]),[k,M]=(0,r.useState)(!0),[q,z]=(0,r.useState)(""),[E,D]=(0,r.useState)(null),[C,S]=(0,r.useState)(""),[P,G]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let s=async()=>{try{M(!0);let e=await (0,A.KV)();N(e)}catch(e){console.error("Error fetching custom requests:",e),z("Failed to load custom requests")}finally{M(!1)}};e&&(null==t?void 0:t.role)==="admin"&&s()},[e,t]);let H=async(e,t)=>{try{G(!0),await (0,A.gW)(e,t,C),N(s=>s.map(s=>s.id===e?{...s,status:t,adminNotes:C,updatedAt:new Date}:s)),D(null),S("")}catch(e){console.error("Error updating request status:",e),z("Failed to update request status")}finally{G(!1)}};if(!e||(null==t?void 0:t.role)!=="admin")return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You need admin privileges to access this page."}),(0,a.jsx)(d.$,{asChild:!0,children:(0,a.jsx)(w(),{href:"/dashboard",children:"Go to Dashboard"})})]});let Z=e=>{switch(e){case"pending":default:return(0,a.jsx)(x.A,{className:"h-4 w-4"});case"in-progress":return(0,a.jsx)(m,{className:"h-4 w-4"});case"completed":return(0,a.jsx)(h.A,{className:"h-4 w-4"});case"cancelled":return(0,a.jsx)(p.A,{className:"h-4 w-4"})}},_=e=>{switch(e){case"pending":default:return"outline";case"in-progress":return"secondary";case"completed":return"default";case"cancelled":return"destructive"}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Custom Requests Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage and respond to custom design requests from users"})]}),k&&(0,a.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading custom requests..."})]})}),q&&(0,a.jsx)(u.Fc,{variant:"destructive",className:"mb-6",children:(0,a.jsx)(u.TN,{children:q})}),!k&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-yellow-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,a.jsx)("p",{className:"text-xl font-bold",children:s.filter(e=>"pending"===e.status).length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"In Progress"}),(0,a.jsx)("p",{className:"text-xl font-bold",children:s.filter(e=>"in-progress"===e.status).length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,a.jsx)("p",{className:"text-xl font-bold",children:s.filter(e=>"completed"===e.status).length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-gray-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total"}),(0,a.jsx)("p",{className:"text-xl font-bold",children:s.length})]})]})})})]}),!k&&(0,a.jsx)("div",{className:"space-y-4",children:s.length>0?s.map(e=>(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,a.jsxs)(c.E,{variant:_(e.status),className:"flex items-center space-x-1",children:[Z(e.status),(0,a.jsx)("span",{children:e.status})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.userEmail})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.category})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:new Date(e.createdAt).toLocaleDateString()})]})]}),e.budget&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-3",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Budget: $",e.budget]})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:e.description}),e.adminNotes&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Admin Notes"})]}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:e.adminNotes})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2 ml-4",children:["pending"===e.status&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(d.$,{size:"sm",onClick:()=>D(e),children:"Manage"})}),"in-progress"===e.status&&(0,a.jsx)(d.$,{size:"sm",variant:"outline",onClick:()=>D(e),children:"Update"})]})]})})},e.id)):(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-12 text-center",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Custom Requests"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No custom design requests have been submitted yet."})]})})}),E&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)(n.Zp,{className:"w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{children:["Manage Request: ",E.title]}),(0,a.jsx)(n.BT,{children:"Update the status and add admin notes for this request"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"adminNotes",children:"Admin Notes"}),(0,a.jsx)(l.T,{id:"adminNotes",placeholder:"Add notes about this request...",value:C,onChange:e=>S(e.target.value),rows:4})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:["pending"===E.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(d.$,{onClick:()=>H(E.id,"in-progress"),disabled:P,children:[P&&(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Accept & Start"]}),(0,a.jsxs)(d.$,{variant:"destructive",onClick:()=>H(E.id,"cancelled"),disabled:P,children:[P&&(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Decline"]})]}),"in-progress"===E.status&&(0,a.jsxs)(d.$,{onClick:()=>H(E.id,"completed"),disabled:P,children:[P&&(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Mark Complete"]}),(0,a.jsx)(d.$,{variant:"outline",onClick:()=>{D(null),S("")},children:"Cancel"})]})]})]})})]})}},3655:(e,t,s)=>{"use strict";s.d(t,{hO:()=>c,sG:()=>d});var a=s(2115),r=s(7650),i=s(9708),n=s(5155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,i.TL)(`Primitive.${t}`),r=a.forwardRef((e,a)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(r?s:t,{...i,ref:a})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function c(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5057:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(5155);s(2115);var r=s(968),i=s(9434);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},5365:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>d,TN:()=>c});var a=s(5155);s(2115);var r=s(2085),i=s(9434);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:s}),t),...r})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...s})}},5868:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j:()=>d});var a=s(3915),r=s(6203),i=s(5317);let n=(0,a.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),d=(0,r.xI)(n),c=(0,i.aU)(n)},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var a=s(5155);s(2115);var r=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,asChild:i=!1,...c}=e,l=i?r.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:s}),t),...c})}},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},7434:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},8539:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(2596),r=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,455,645,874,441,684,358],()=>t(2501)),_N_E=e.O()}]);