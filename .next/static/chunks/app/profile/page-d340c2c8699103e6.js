(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>c});var s=a(5155),r=a(2115),n=a(6203),i=a(5317),l=a(6104);let d=(0,r.createContext)(void 0),o=()=>{let e=(0,r.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[a,o]=(0,r.useState)(null),[c,u]=(0,r.useState)(null),[m,x]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,n.hg)(l.j,async e=>{if(e){o(e);try{let t=await (0,i.x7)((0,i.H9)(l.db,"users",e.uid));if(t.exists())u(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,i.BN)((0,i.H9)(l.db,"users",e.uid),t),u(t)}}catch(t){console.error("Error fetching/creating user data:",t),u({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else o(null),u(null);x(!1)});return()=>e()},[]);let p=async(e,t)=>{try{await (0,n.x9)(l.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},h=async(e,t)=>{try{let{user:a}=await (0,n.eJ)(l.j,e,t),s={uid:a.uid,email:a.email,role:"user",displayName:a.displayName||"",createdAt:new Date};await (0,i.BN)((0,i.H9)(l.db,"users",a.uid),s)}catch(e){throw console.error("Sign up error:",e),e}},v=async()=>{await (0,n.CI)(l.j)},g=async e=>{if(!a)throw Error("No user logged in");try{let t={...e,updatedAt:new Date};await (0,i.BN)((0,i.H9)(l.db,"users",a.uid),t,{merge:!0}),c&&u({...c,...t})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,s.jsx)(d.Provider,{value:{user:a,userData:c,loading:m,signIn:p,signUp:h,logout:v,updateUserProfile:g},children:t})}},285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:t})),...o})}},968:(e,t,a)=>{"use strict";a.d(t,{b:()=>l});var s=a(2115),r=a(3655),n=a(5155),i=s.forwardRef((e,t)=>(0,n.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},1007:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1608:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(5155),r=a(2115),n=a(283),i=a(6695),l=a(285),d=a(2523),o=a(5057),c=a(9409),u=a(5365),m=a(1007),x=a(8883),p=a(9420),h=a(9074),v=a(4869),g=a(1154);let f=(0,a(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var y=a(6874),b=a.n(y);let j=[{code:"+1",country:"US/CA"},{code:"+44",country:"UK"},{code:"+91",country:"India"},{code:"+86",country:"China"},{code:"+81",country:"Japan"},{code:"+49",country:"Germany"},{code:"+33",country:"France"},{code:"+39",country:"Italy"},{code:"+34",country:"Spain"},{code:"+61",country:"Australia"},{code:"+55",country:"Brazil"},{code:"+52",country:"Mexico"},{code:"+7",country:"Russia"},{code:"+82",country:"South Korea"},{code:"+65",country:"Singapore"},{code:"+971",country:"UAE"},{code:"+966",country:"Saudi Arabia"},{code:"+27",country:"South Africa"}];function N(){let{user:e,userData:t,updateUserProfile:a}=(0,n.A)(),[y,N]=(0,r.useState)(!1),[w,A]=(0,r.useState)(!1),[k,z]=(0,r.useState)(""),[C,S]=(0,r.useState)({fullName:(null==t?void 0:t.fullName)||"",displayName:(null==t?void 0:t.displayName)||"",phoneNumber:(null==t?void 0:t.phoneNumber)||"",countryCode:(null==t?void 0:t.countryCode)||"+1"});if(!e)return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Please sign in to access your profile"}),(0,s.jsx)(l.$,{asChild:!0,children:(0,s.jsx)(b(),{href:"/auth",children:"Sign In"})})]});let _=e=>{S(t=>({...t,[e.target.name]:e.target.value})),z(""),A(!1)},M=async e=>{if(e.preventDefault(),N(!0),z(""),A(!1),!C.fullName.trim()){z("Full name is required"),N(!1);return}if(C.phoneNumber&&!/^\d{10,15}$/.test(C.phoneNumber.replace(/\s/g,""))){z("Please enter a valid phone number (10-15 digits)"),N(!1);return}try{await a({fullName:C.fullName.trim(),displayName:C.displayName.trim()||C.fullName.trim(),phoneNumber:C.phoneNumber.trim(),countryCode:C.countryCode}),A(!0)}catch(e){z(e.message||"Failed to update profile")}finally{N(!1)}};return(0,s.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,s.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"Profile Settings"}),(0,s.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Manage your account information and preferences"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Account Information"]}),(0,s.jsx)(i.BT,{children:"View your account details and status"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,s.jsxs)("div",{className:"flex items-center mt-1",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.email})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Phone Number"}),(0,s.jsxs)("div",{className:"flex items-center mt-1",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:(null==t?void 0:t.phoneNumber)?"".concat(t.countryCode||"+1"," ").concat(t.phoneNumber):"Not provided"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Full Name"}),(0,s.jsxs)("div",{className:"flex items-center mt-1",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:(null==t?void 0:t.fullName)||"Not provided"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Member Since"}),(0,s.jsxs)("div",{className:"flex items-center mt-1",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:(null==t?void 0:t.createdAt)?new Date(t.createdAt.seconds?1e3*t.createdAt.seconds:t.createdAt).toLocaleDateString():"Recently joined"})]})]})]})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Personal Information"}),(0,s.jsx)(i.BT,{children:"Update your personal details and contact information"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("form",{onSubmit:M,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"fullName",children:"Full Name *"}),(0,s.jsx)(d.p,{id:"fullName",name:"fullName",type:"text",placeholder:"Enter your full name",value:C.fullName,onChange:_,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"displayName",children:"Display Name"}),(0,s.jsx)(d.p,{id:"displayName",name:"displayName",type:"text",placeholder:"How others see your name",value:C.displayName,onChange:_})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{children:"Phone Number"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(c.l6,{value:C.countryCode,onValueChange:e=>{S(t=>({...t,countryCode:e})),z(""),A(!1)},children:[(0,s.jsxs)(c.bq,{className:"w-32",children:[(0,s.jsx)(v.A,{className:"w-4 h-4 mr-2"}),(0,s.jsx)(c.yv,{})]}),(0,s.jsx)(c.gC,{children:j.map(e=>(0,s.jsxs)(c.eb,{value:e.code,children:[e.code," (",e.country,")"]},e.code))})]}),(0,s.jsx)(d.p,{name:"phoneNumber",type:"tel",placeholder:"Enter phone number",value:C.phoneNumber,onChange:_,className:"flex-1"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Enter your phone number without the country code"})]}),k&&(0,s.jsx)(u.Fc,{variant:"destructive",children:(0,s.jsx)(u.TN,{children:k})}),w&&(0,s.jsx)(u.Fc,{className:"border-green-200 bg-green-50",children:(0,s.jsx)(u.TN,{className:"text-green-800",children:"Profile updated successfully!"})}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)(l.$,{type:"submit",disabled:y,children:[y&&(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,s.jsx)(f,{className:"mr-2 h-4 w-4"}),"Save Changes"]}),(0,s.jsx)(l.$,{type:"button",variant:"outline",asChild:!0,children:(0,s.jsx)(b(),{href:"/dashboard",children:"Cancel"})})]})]})})]})]})]})})}},1700:(e,t,a)=>{Promise.resolve().then(a.bind(a,1608))},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},4869:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var s=a(5155);a(2115);var r=a(968),n=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},5365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>l,TN:()=>d});var s=a(5155);a(2115);var r=a(2085),n=a(9434);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:a}),t),...r})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>d,j:()=>l});var s=a(3915),r=a(6203),n=a(5317);let i=(0,s.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),l=(0,r.xI)(i),d=(0,n.aU)(i)},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},8883:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>o,yv:()=>c});var s=a(5155);a(2115);var r=a(8715),n=a(6474),i=a(5196),l=a(7863),d=a(9434);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:i,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[i,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,s.jsx)(p,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(h,{})]})})}function x(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}},9420:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,455,645,874,942,242,171,441,684,358],()=>t(1700)),_N_E=e.O()}]);