(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[582],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var r=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:i,asChild:d=!1,...c}=e,o=d?a.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:s})),...c})}},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1788:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2138:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},5431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(5155),a=t(2115),i=t(6695),n=t(285),l=t(6126),d=t(646);let c=(0,t(9946).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var o=t(8564),u=t(1788),h=t(2138),x=t(6874),m=t.n(x),g=t(5695);function p(){let e=(0,g.useRouter)();return(0,a.useEffect)(()=>{let s=setTimeout(()=>{e.push("/dashboard")},1e4);return()=>clearTimeout(s)},[e]),(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"max-w-2xl w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative inline-block",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse",children:(0,r.jsx)(d.A,{className:"h-12 w-12 text-green-600"})}),(0,r.jsx)("div",{className:"absolute -top-2 -right-2",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center animate-bounce",children:(0,r.jsx)(c,{className:"h-4 w-4 text-yellow-600"})})})]}),(0,r.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"\uD83C\uDF89 Purchase Successful!"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"Thank you for your purchase! Your template is ready for download."})]}),(0,r.jsxs)(i.Zp,{className:"border-0 shadow-xl",children:[(0,r.jsxs)(i.aR,{className:"text-center pb-4",children:[(0,r.jsx)(i.ZB,{className:"text-xl",children:"Your Template is Ready!"}),(0,r.jsx)(i.BT,{children:"You can now download and start using your premium template"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:"Creative Portfolio Template"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Professional portfolio design"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"₹1,999"}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-yellow-500",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 fill-current"}),(0,r.jsx)("span",{className:"text-sm",children:"4.9"})]})]})]})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"What's Included:"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:"Source Files"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:"Documentation"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:"Lifetime Updates"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:"Premium Support"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(n.$,{className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white h-12",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Download Template"]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,r.jsx)(m(),{href:"/dashboard",children:"View Dashboard"})}),(0,r.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,r.jsxs)(m(),{href:"/templates",children:["Browse More",(0,r.jsx)(h.A,{className:"h-4 w-4 ml-2"})]})})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Next Steps:"}),(0,r.jsxs)("ol",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"1. Download your template files"}),(0,r.jsx)("li",{children:"2. Read the documentation"}),(0,r.jsx)("li",{children:"3. Customize to your needs"}),(0,r.jsx)("li",{children:"4. Launch your project!"})]})]}),(0,r.jsxs)("div",{className:"text-center text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:["Need help? ",(0,r.jsx)(m(),{href:"/contact",className:"text-blue-600 hover:underline",children:"Contact our support team"})]}),(0,r.jsx)("p",{className:"mt-2",children:"Redirecting to dashboard in 10 seconds..."})]})]})]}),(0,r.jsx)(i.Zp,{className:"border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50",children:(0,r.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,r.jsxs)(l.E,{className:"mb-3 bg-yellow-500",children:[(0,r.jsx)(c,{className:"h-4 w-4 mr-1"}),"Special Offer"]}),(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Get 20% off your next purchase!"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Use code ",(0,r.jsx)("code",{className:"bg-yellow-200 px-2 py-1 rounded font-mono",children:"WELCOME20"})," on your next template purchase"]}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsx)(m(),{href:"/templates",children:"Shop More Templates"})})]})})]})})}},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var r=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,asChild:i=!1,...d}=e,c=i?a.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),s),...d})}},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n});var r=t(5155);t(2115);var a=t(9434);function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}},7394:(e,s,t)=>{Promise.resolve().then(t.bind(t,5431))},8564:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(2596),a=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[455,874,441,684,358],()=>s(7394)),_N_E=e.O()}]);