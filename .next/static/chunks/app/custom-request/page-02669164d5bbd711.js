(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[372],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>c,AuthProvider:()=>o});var s=a(5155),r=a(2115),i=a(6203),n=a(5317),d=a(6104);let l=(0,r.createContext)(void 0),c=()=>{let e=(0,r.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},o=e=>{let{children:t}=e,[a,c]=(0,r.useState)(null),[o,u]=(0,r.useState)(null),[x,m]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(d.j,async e=>{if(e){c(e);try{let t=await (0,n.x7)((0,n.H9)(d.db,"users",e.uid));if(t.exists())u(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",e.uid),t),u(t)}}catch(t){console.error("Error fetching/creating user data:",t),u({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else c(null),u(null);m(!1)});return()=>e()},[]);let p=async(e,t)=>{try{await (0,i.x9)(d.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},h=async(e,t)=>{try{let{user:a}=await (0,i.eJ)(d.j,e,t),s={uid:a.uid,email:a.email,role:"user",displayName:a.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",a.uid),s)}catch(e){throw console.error("Sign up error:",e),e}},g=async()=>{await (0,i.CI)(d.j)},v=async e=>{if(!a)throw Error("No user logged in");try{let t={...e,updatedAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",a.uid),t,{merge:!0}),o&&u({...o,...t})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,s.jsx)(l.Provider,{value:{user:a,userData:o,loading:x,signIn:p,signUp:h,logout:g,updateUserProfile:v},children:t})}},285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var s=a(5155);a(2115);var r=a(9708),i=a(2085),n=a(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:i,asChild:l=!1,...c}=e,o=l?r.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:a,size:i,className:t})),...c})}},646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},968:(e,t,a)=>{"use strict";a.d(t,{b:()=>d});var s=a(2115),r=a(3655),i=a(5155),n=s.forwardRef((e,t)=>(0,i.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var d=n},1154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2288:(e,t,a)=>{"use strict";a.d(t,{CF:()=>l,CV:()=>u,FQ:()=>m,KV:()=>n,SZ:()=>p,Tb:()=>x,Xm:()=>o,gW:()=>d,hx:()=>i,wx:()=>c});var s=a(5317),r=a(6104);let i=async e=>{try{return(await (0,s.gS)((0,s.rJ)(r.db,"customRequests"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating custom request:",e),e}},n=async()=>{try{let e=(0,s.P)((0,s.rJ)(r.db,"customRequests"),(0,s.My)("createdAt","desc"));return(await (0,s.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching custom requests:",e),e}},d=async(e,t,a)=>{try{let i={status:t,updatedAt:new Date};a&&(i.adminNotes=a),await (0,s.mZ)((0,s.H9)(r.db,"customRequests",e),i)}catch(e){throw console.error("Error updating custom request:",e),e}},l=async()=>{try{let e=(0,s.P)((0,s.rJ)(r.db,"users"),(0,s.My)("createdAt","desc"));return(await (0,s.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching users:",e),e}},c=async()=>{try{let[e,t,a]=await Promise.all([(0,s.GG)((0,s.rJ)(r.db,"users")),(0,s.GG)((0,s.rJ)(r.db,"templates")),(0,s.GG)((0,s.rJ)(r.db,"customRequests"))]),i=e.size,n=t.size,d=a.size,l=a.docs.filter(e=>"pending"===e.data().status).length;return{totalUsers:i,totalTemplates:n,totalRequests:d,pendingRequests:l,totalSales:0,customizations:0}}catch(e){throw console.error("Error fetching dashboard stats:",e),e}},o=e=>{let t=(0,s.P)((0,s.rJ)(r.db,"customRequests"),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})},u=async e=>{try{return(await (0,s.gS)((0,s.rJ)(r.db,"contactMessages"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating contact message:",e),e}},x=async()=>{try{let e=(0,s.P)((0,s.rJ)(r.db,"contactMessages"),(0,s.My)("createdAt","desc"));return(await (0,s.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching contact messages:",e),e}},m=async(e,t,a)=>{try{let i={status:t,updatedAt:new Date};a&&(i.adminNotes=a),await (0,s.mZ)((0,s.H9)(r.db,"contactMessages",e),i)}catch(e){throw console.error("Error updating contact message:",e),e}},p=e=>{let t=(0,s.P)((0,s.rJ)(r.db,"contactMessages"),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})}},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var s=a(5155);a(2115);var r=a(9434);function i(e){let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},3127:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var s=a(5155);a(2115);var r=a(968),i=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},5365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>d,TN:()=>l});var s=a(5155);a(2115);var r=a(2085),i=a(9434);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:a}),t),...r})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j:()=>d});var s=a(3915),r=a(6203),i=a(5317);let n=(0,s.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),d=(0,r.xI)(n),l=(0,i.aU)(n)},6126:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var s=a(5155);a(2115);var r=a(9708),i=a(2085),n=a(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:i=!1,...l}=e,c=i?r.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(d({variant:a}),t),...l})}},6540:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>z});var s=a(5155),r=a(2115),i=a(283),n=a(285),d=a(2523),l=a(5057),c=a(8539),o=a(6695),u=a(9409),x=a(5365),m=a(6126),p=a(3127),h=a(9946);let g=(0,h.A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);var v=a(7580);let f=(0,h.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);var b=a(646),y=a(1154),j=a(6874),w=a.n(j),N=a(2288);let k=["Dashboard","E-commerce","Landing Page","Portfolio","Corporate","Mobile App","Blog/CMS","Other"],A=[{icon:p.A,title:"Custom Design",description:"Tailored specifically to your brand and requirements"},{icon:g,title:"Fast Delivery",description:"Most projects completed within 5-10 business days"},{icon:v.A,title:"Expert Team",description:"Experienced designers and developers working on your project"},{icon:f,title:"Full Documentation",description:"Complete setup guide and documentation included"}];function z(){let{user:e,userData:t}=(0,i.A)(),[a,p]=(0,r.useState)(!1),[h,g]=(0,r.useState)(!1),[v,f]=(0,r.useState)(""),[j,z]=(0,r.useState)({title:"",description:"",category:"",budget:"",deadline:"",features:"",inspiration:""}),C=e=>{z(t=>({...t,[e.target.name]:e.target.value})),f("")},D=async t=>{if(t.preventDefault(),p(!0),f(""),!j.title||!j.description||!j.category){f("Please fill in all required fields"),p(!1);return}try{await (0,N.hx)({userId:e.uid,userEmail:e.email,title:j.title,description:j.description,category:j.category,budget:j.budget?parseFloat(j.budget.replace(/[^0-9.-]+/g,"")):void 0,deadline:j.deadline?new Date(j.deadline):void 0,status:"pending"}),g(!0)}catch(e){f(e.message||"Failed to submit request")}finally{p(!1)}};return e?h?(0,s.jsx)("div",{className:"container mx-auto px-4 py-20",children:(0,s.jsx)(o.Zp,{className:"max-w-2xl mx-auto text-center",children:(0,s.jsxs)(o.Wu,{className:"p-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)(b.A,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Request Submitted!"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Thank you for your custom design request. Our team will review it and get back to you within 24 hours."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(n.$,{asChild:!0,className:"w-full",children:(0,s.jsx)(w(),{href:"/dashboard",children:"Go to Dashboard"})}),(0,s.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,s.jsx)(w(),{href:"/templates",children:"Browse Templates"})})]})]})})}):(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8 sm:mb-12",children:[(0,s.jsx)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4",children:"Request Custom Design"}),(0,s.jsx)("p",{className:"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto",children:"Can't find what you're looking for? Let our expert team create a custom template tailored to your specific needs."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6 lg:gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"Project Details"}),(0,s.jsx)(o.BT,{children:"Tell us about your project requirements and we'll create something amazing for you."})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("form",{onSubmit:D,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"title",children:"Project Title *"}),(0,s.jsx)(d.p,{id:"title",name:"title",placeholder:"e.g., Modern SaaS Dashboard",value:j.title,onChange:C,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"category",children:"Category *"}),(0,s.jsxs)(u.l6,{value:j.category,onValueChange:e=>{z(t=>({...t,category:e}))},children:[(0,s.jsx)(u.bq,{children:(0,s.jsx)(u.yv,{placeholder:"Select a category"})}),(0,s.jsx)(u.gC,{children:k.map(e=>(0,s.jsx)(u.eb,{value:e,children:e},e))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"description",children:"Project Description *"}),(0,s.jsx)(c.T,{id:"description",name:"description",placeholder:"Describe your project in detail. What is the purpose? Who is the target audience? What features do you need?",value:j.description,onChange:C,rows:4,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"features",children:"Required Features"}),(0,s.jsx)(c.T,{id:"features",name:"features",placeholder:"List specific features you need (e.g., user authentication, payment integration, responsive design, etc.)",value:j.features,onChange:C,rows:3})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"budget",children:"Budget Range (USD)"}),(0,s.jsx)(d.p,{id:"budget",name:"budget",placeholder:"e.g., $500 - $1000",value:j.budget,onChange:C})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"deadline",children:"Preferred Deadline"}),(0,s.jsx)(d.p,{id:"deadline",name:"deadline",type:"date",value:j.deadline,onChange:C})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"inspiration",children:"Inspiration/References"}),(0,s.jsx)(c.T,{id:"inspiration",name:"inspiration",placeholder:"Share any websites, designs, or references that inspire you. Include URLs if possible.",value:j.inspiration,onChange:C,rows:3})]}),v&&(0,s.jsx)(x.Fc,{variant:"destructive",children:(0,s.jsx)(x.TN,{children:v})}),(0,s.jsxs)(n.$,{type:"submit",className:"w-full",disabled:a,children:[a&&(0,s.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Submit Request"]})]})})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"Why Choose Custom Design?"})}),(0,s.jsx)(o.Wu,{className:"space-y-4",children:A.map((e,t)=>{let a=e.icon;return(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(a,{className:"h-5 w-5 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},t)})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"Our Process"})}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Submit Request"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Fill out the form with your requirements"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Review & Quote"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"We'll review and send you a detailed quote"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Design & Develop"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Our team creates your custom template"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Delivery"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Receive your completed template with documentation"})]})]})]})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"Need Help?"})}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Have questions about custom design? Our team is here to help."}),(0,s.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,s.jsx)(w(),{href:"/contact",children:"Contact Us"})})]})]})]})]})]}):(0,s.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Sign in Required"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Please sign in to submit a custom design request."}),(0,s.jsx)(n.$,{asChild:!0,children:(0,s.jsx)(w(),{href:"/auth",children:"Sign In"})})]})}},6632:(e,t,a)=>{Promise.resolve().then(a.bind(a,6540))},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var s=a(5155);a(2115);var r=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},7580:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8539:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var s=a(5155);a(2115);var r=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},9409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>c,yv:()=>o});var s=a(5155);a(2115);var r=a(8715),i=a(6474),n=a(5196),d=a(7863),l=a(9434);function c(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function o(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:n,...d}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,s.jsx)(p,{}),(0,s.jsx)(r.LM,{className:(0,l.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(h,{})]})})}function m(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(d.A,{className:"size-4"})})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var s=a(2596),r=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,455,645,874,942,242,171,441,684,358],()=>t(6632)),_N_E=e.O()}]);