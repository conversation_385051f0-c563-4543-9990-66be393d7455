(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{283:(e,a,t)=>{"use strict";t.d(a,{A:()=>o,AuthProvider:()=>c});var s=t(5155),r=t(2115),i=t(6203),n=t(5317),d=t(6104);let l=(0,r.createContext)(void 0),o=()=>{let e=(0,r.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:a}=e,[t,o]=(0,r.useState)(null),[c,u]=(0,r.useState)(null),[m,p]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(d.j,async e=>{if(e){o(e);try{let a=await (0,n.x7)((0,n.H9)(d.db,"users",e.uid));if(a.exists())u(a.data());else{let a={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",e.uid),a),u(a)}}catch(a){console.error("Error fetching/creating user data:",a),u({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else o(null),u(null);p(!1)});return()=>e()},[]);let h=async(e,a)=>{try{await (0,i.x9)(d.j,e,a)}catch(e){throw console.error("Sign in error:",e),e}},x=async(e,a)=>{try{let{user:t}=await (0,i.eJ)(d.j,e,a),s={uid:t.uid,email:t.email,role:"user",displayName:t.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",t.uid),s)}catch(e){throw console.error("Sign up error:",e),e}},g=async()=>{await (0,i.CI)(d.j)},v=async e=>{if(!t)throw Error("No user logged in");try{let a={...e,updatedAt:new Date};await (0,n.BN)((0,n.H9)(d.db,"users",t.uid),a,{merge:!0}),c&&u({...c,...a})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,s.jsx)(l.Provider,{value:{user:t,userData:c,loading:m,signIn:h,signUp:x,logout:g,updateUserProfile:v},children:a})}},285:(e,a,t)=>{"use strict";t.d(a,{$:()=>l});var s=t(5155);t(2115);var r=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:t,size:i,asChild:l=!1,...o}=e,c=l?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:a})),...o})}},851:(e,a,t)=>{Promise.resolve().then(t.bind(t,7883))},2523:(e,a,t)=>{"use strict";t.d(a,{p:()=>i});var s=t(5155);t(2115);var r=t(9434);function i(e){let{className:a,type:t,...i}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...i})}},5057:(e,a,t)=>{"use strict";t.d(a,{J:()=>n});var s=t(5155);t(2115);var r=t(968),i=t(9434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},5365:(e,a,t)=>{"use strict";t.d(a,{Fc:()=>d,TN:()=>l});var s=t(5155);t(2115);var r=t(2085),i=t(9434);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:a,variant:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),a),...r})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...t})}},6104:(e,a,t)=>{"use strict";t.d(a,{db:()=>l,j:()=>d});var s=t(3915),r=t(6203),i=t(5317);let n=(0,s.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),d=(0,r.xI)(n),l=(0,i.aU)(n)},6695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n});var s=t(5155);t(2115);var r=t(9434);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},7883:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>N});var s=t(5155),r=t(2115),i=t(5695),n=t(283),d=t(285),l=t(2523),o=t(5057),c=t(6695),u=t(704),m=t(9434);function p(e){let{className:a,...t}=e;return(0,s.jsx)(u.bL,{"data-slot":"tabs",className:(0,m.cn)("flex flex-col gap-2",a),...t})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(u.B8,{"data-slot":"tabs-list",className:(0,m.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...t})}function x(e){let{className:a,...t}=e;return(0,s.jsx)(u.l9,{"data-slot":"tabs-trigger",className:(0,m.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...t})}function g(e){let{className:a,...t}=e;return(0,s.jsx)(u.UC,{"data-slot":"tabs-content",className:(0,m.cn)("flex-1 outline-none",a),...t})}var v=t(5365),f=t(8749),b=t(2657),w=t(1154),y=t(6874),j=t.n(y);function N(){let[e,a]=(0,r.useState)(!1),[t,u]=(0,r.useState)(""),[m,y]=(0,r.useState)(!1),[N,k]=(0,r.useState)({email:"",password:"",confirmPassword:""}),{signIn:A,signUp:C,user:P,userData:S}=(0,n.A)(),_=(0,i.useRouter)(),E=(0,i.useSearchParams)().get("mode")||"signin";(0,r.useEffect)(()=>{P&&S&&("admin"===S.role?_.push("/admin"):_.push("/dashboard"))},[P,S,_]);let F=e=>{k(a=>({...a,[e.target.name]:e.target.value})),u("")},I=async e=>{e.preventDefault(),a(!0),u("");try{await A(N.email,N.password)}catch(a){let e="Failed to sign in";"auth/user-not-found"===a.code?e="No account found with this email address":"auth/wrong-password"===a.code?e="Incorrect password":"auth/invalid-email"===a.code?e="Invalid email address":"auth/too-many-requests"===a.code&&(e="Too many failed attempts. Please try again later"),u(e)}finally{a(!1)}},z=async e=>{if(e.preventDefault(),a(!0),u(""),N.password!==N.confirmPassword){u("Passwords do not match"),a(!1);return}if(N.password.length<6){u("Password must be at least 6 characters"),a(!1);return}try{await C(N.email,N.password)}catch(a){let e="Failed to create account";"auth/email-already-in-use"===a.code?e="An account with this email already exists":"auth/invalid-email"===a.code?e="Invalid email address":"auth/weak-password"===a.code&&(e="Password should be at least 6 characters"),u(e)}finally{a(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsxs)(j(),{href:"/",className:"inline-flex items-center space-x-2 mb-6",children:[(0,s.jsx)("div",{className:"h-10 w-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold",children:"K"})}),(0,s.jsx)("span",{className:"font-bold text-2xl",children:"KaleidoneX"})]})}),(0,s.jsxs)(c.Zp,{children:[(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)(c.ZB,{className:"text-2xl",children:"signup"===E?"Create Account":"Welcome Back"}),(0,s.jsx)(c.BT,{children:"signup"===E?"Sign up to start downloading premium templates":"Sign in to your account to continue"})]}),(0,s.jsxs)(c.Wu,{children:[(0,s.jsxs)(p,{value:E,className:"w-full",children:[(0,s.jsxs)(h,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(x,{value:"signin",asChild:!0,children:(0,s.jsx)(j(),{href:"/auth?mode=signin",children:"Sign In"})}),(0,s.jsx)(x,{value:"signup",asChild:!0,children:(0,s.jsx)(j(),{href:"/auth?mode=signup",children:"Sign Up"})})]}),(0,s.jsx)(g,{value:"signin",children:(0,s.jsxs)("form",{onSubmit:I,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(l.p,{id:"email",name:"email",type:"email",placeholder:"Enter your email",value:N.email,onChange:F,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{id:"password",name:"password",type:m?"text":"password",placeholder:"Enter your password",value:N.password,onChange:F,required:!0}),(0,s.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>y(!m),children:m?(0,s.jsx)(f.A,{className:"h-4 w-4"}):(0,s.jsx)(b.A,{className:"h-4 w-4"})})]})]}),t&&(0,s.jsx)(v.Fc,{variant:"destructive",children:(0,s.jsx)(v.TN,{children:t})}),(0,s.jsxs)(d.$,{type:"submit",className:"w-full",disabled:e,children:[e&&(0,s.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sign In"]})]})}),(0,s.jsx)(g,{value:"signup",children:(0,s.jsxs)("form",{onSubmit:z,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"signup-email",children:"Email"}),(0,s.jsx)(l.p,{id:"signup-email",name:"email",type:"email",placeholder:"Enter your email",value:N.email,onChange:F,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"signup-password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{id:"signup-password",name:"password",type:m?"text":"password",placeholder:"Create a password",value:N.password,onChange:F,required:!0}),(0,s.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>y(!m),children:m?(0,s.jsx)(f.A,{className:"h-4 w-4"}):(0,s.jsx)(b.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,s.jsx)(l.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm your password",value:N.confirmPassword,onChange:F,required:!0})]}),t&&(0,s.jsx)(v.Fc,{variant:"destructive",children:(0,s.jsx)(v.TN,{children:t})}),(0,s.jsxs)(d.$,{type:"submit",className:"w-full",disabled:e,children:[e&&(0,s.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Account"]})]})})]}),(0,s.jsxs)("div",{className:"mt-6 text-center text-sm text-gray-600",children:["By continuing, you agree to our"," ",(0,s.jsx)(j(),{href:"/terms",className:"text-blue-600 hover:underline",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(j(),{href:"/privacy",className:"text-blue-600 hover:underline",children:"Privacy Policy"})]})]})]})]})})}},9434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i});var s=t(2596),r=t(9688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}}},e=>{var a=a=>e(e.s=a);e.O(0,[992,965,455,645,874,942,20,441,684,358],()=>a(851)),_N_E=e.O()}]);