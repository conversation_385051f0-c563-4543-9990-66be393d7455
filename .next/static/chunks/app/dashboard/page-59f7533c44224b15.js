(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{283:(e,s,a)=>{"use strict";a.d(s,{A:()=>c,AuthProvider:()=>o});var t=a(5155),r=a(2115),i=a(6203),d=a(5317),l=a(6104);let n=(0,r.createContext)(void 0),c=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},o=e=>{let{children:s}=e,[a,c]=(0,r.useState)(null),[o,u]=(0,r.useState)(null),[x,m]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(l.j,async e=>{if(e){c(e);try{let s=await (0,d.x7)((0,d.H9)(l.db,"users",e.uid));if(s.exists())u(s.data());else{let s={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,d.BN)((0,d.H9)(l.db,"users",e.uid),s),u(s)}}catch(s){console.error("Error fetching/creating user data:",s),u({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else c(null),u(null);m(!1)});return()=>e()},[]);let h=async(e,s)=>{try{await (0,i.x9)(l.j,e,s)}catch(e){throw console.error("Sign in error:",e),e}},p=async(e,s)=>{try{let{user:a}=await (0,i.eJ)(l.j,e,s),t={uid:a.uid,email:a.email,role:"user",displayName:a.displayName||"",createdAt:new Date};await (0,d.BN)((0,d.H9)(l.db,"users",a.uid),t)}catch(e){throw console.error("Sign up error:",e),e}},v=async()=>{await (0,i.CI)(l.j)},g=async e=>{if(!a)throw Error("No user logged in");try{let s={...e,updatedAt:new Date};await (0,d.BN)((0,d.H9)(l.db,"users",a.uid),s,{merge:!0}),o&&u({...o,...s})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,t.jsx)(n.Provider,{value:{user:a,userData:o,loading:x,signIn:h,signUp:p,logout:v,updateUserProfile:g},children:s})}},285:(e,s,a)=>{"use strict";a.d(s,{$:()=>n});var t=a(5155);a(2115);var r=a(9708),i=a(2085),d=a(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function n(e){let{className:s,variant:a,size:i,asChild:n=!1,...c}=e,o=n?r.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,d.cn)(l({variant:a,size:i,className:s})),...c})}},1788:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4879:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>j});var t=a(5155);a(2115);var r=a(283),i=a(6695),d=a(285),l=a(6126),n=a(6151),c=a(1788),o=a(8564),u=a(4186),x=a(2657),m=a(4616),h=a(6874),p=a.n(h);let v={totalPurchases:12,totalDownloads:45,favoriteTemplates:8,pendingOrders:2},g=[{id:"1",templateName:"Modern Dashboard Pro",purchaseDate:"2024-01-15",amount:49,status:"completed"},{id:"2",templateName:"E-commerce Store Template",purchaseDate:"2024-01-10",amount:79,status:"completed"},{id:"3",templateName:"Landing Page Bundle",purchaseDate:"2024-01-08",amount:39,status:"pending"}];function j(){var e;let{user:s,userData:a}=(0,r.A)();return s?(0,t.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",children:[(0,t.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,t.jsxs)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:["Welcome back, ",(null==a?void 0:a.displayName)||(null==(e=s.email)?void 0:e.split("@")[0]),"!"]}),(0,t.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Manage your templates, orders, and account settings from your dashboard."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8",children:[(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Purchases"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:v.totalPurchases})]}),(0,t.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,t.jsx)(n.A,{className:"h-6 w-6 text-blue-600"})})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Downloads"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:v.totalDownloads})]}),(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,t.jsx)(c.A,{className:"h-6 w-6 text-green-600"})})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Favorites"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:v.favoriteTemplates})]}),(0,t.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,t.jsx)(o.A,{className:"h-6 w-6 text-yellow-600"})})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:v.pendingOrders})]}),(0,t.jsx)("div",{className:"p-3 bg-orange-100 rounded-lg",children:(0,t.jsx)(u.A,{className:"h-6 w-6 text-orange-600"})})]})})})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Recent Purchases"}),(0,t.jsx)(i.BT,{children:"Your latest template purchases and downloads"})]}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("div",{className:"space-y-4",children:g.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.templateName}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Purchased on ",new Date(e.purchaseDate).toLocaleDateString()]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(l.E,{variant:"completed"===e.status?"default":"secondary",children:e.status}),(0,t.jsxs)("span",{className:"font-medium",children:["$",e.amount]}),"completed"===e.status&&(0,t.jsxs)(d.$,{size:"sm",variant:"outline",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-1"}),"Download"]})]})]},e.id))}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(d.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,t.jsx)(p(),{href:"/orders",children:"View All Orders"})})})]})]})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Quick Actions"}),(0,t.jsx)(i.BT,{children:"Common tasks and shortcuts"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsx)(d.$,{asChild:!0,className:"w-full justify-start",children:(0,t.jsxs)(p(),{href:"/templates",children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Browse Templates"]})}),(null==a?void 0:a.role)==="admin"&&(0,t.jsx)(d.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)(p(),{href:"/admin/setup",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Setup Sample Data"]})}),(0,t.jsx)(d.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)(p(),{href:"/orders",children:[(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"View Orders"]})}),(0,t.jsx)(d.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)(p(),{href:"/favorites",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"My Favorites"]})})]})]}),(0,t.jsxs)(i.Zp,{className:"mt-6",children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{children:"Account Information"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:s.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Account Type"}),(0,t.jsx)(l.E,{variant:(null==a?void 0:a.role)==="admin"?"default":"secondary",children:(null==a?void 0:a.role)||"user"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Member Since"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:(null==a?void 0:a.createdAt)?new Date(a.createdAt).toLocaleDateString():"N/A"})]})]})})]})]})]})]}):(0,t.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Please sign in to access your dashboard"}),(0,t.jsx)(d.$,{asChild:!0,children:(0,t.jsx)(p(),{href:"/auth",children:"Sign In"})})]})}},5171:(e,s,a)=>{Promise.resolve().then(a.bind(a,4879))},6104:(e,s,a)=>{"use strict";a.d(s,{db:()=>n,j:()=>l});var t=a(3915),r=a(6203),i=a(5317);let d=(0,t.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),l=(0,r.xI)(d),n=(0,i.aU)(d)},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>n});var t=a(5155);a(2115);var r=a(9708),i=a(2085),d=a(9434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:a,asChild:i=!1,...n}=e,c=i?r.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,d.cn)(l({variant:a}),s),...n})}},6151:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>n,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>d});var t=a(5155);a(2115);var r=a(9434);function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}},8564:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>i});var t=a(2596),r=a(9688);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,455,645,874,441,684,358],()=>s(5171)),_N_E=e.O()}]);