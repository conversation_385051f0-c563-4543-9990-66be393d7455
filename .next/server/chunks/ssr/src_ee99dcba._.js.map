{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  query,\n  orderBy,\n  onSnapshot\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...requestData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n    \n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n    \n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'contactMessages'), {\n      ...messageData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating contact message:', error);\n    throw error;\n  }\n};\n\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    await updateDoc(doc(db, 'contactMessages', messageId), updateData);\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n\nexport const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {\n  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const messages = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n    callback(messages);\n  });\n};\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAWA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB;YAC7D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB,QAAkC;IACpG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,CAAC;IACzC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAExE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/StatusDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  ChevronDown,\n  Clock,\n  PlayCircle,\n  CheckCircle,\n  XCircle,\n  MessageSquare,\n  Loader2\n} from 'lucide-react';\n\ninterface StatusDropdownProps {\n  currentStatus: string;\n  onStatusChange: (status: string) => Promise<void>;\n  type: 'custom-request' | 'contact-message' | 'purchase-request';\n  disabled?: boolean;\n}\n\nconst getStatusConfig = (status: string, type: 'custom-request' | 'contact-message' | 'purchase-request') => {\n  if (type === 'custom-request') {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'in-progress':\n        return { icon: PlayCircle, label: 'In Progress', variant: 'default' as const, color: 'text-blue-600' };\n      case 'completed':\n        return { icon: CheckCircle, label: 'Completed', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'cancelled':\n        return { icon: XCircle, label: 'Cancelled', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  } else if (type === 'purchase-request') {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'confirmed':\n        return { icon: CheckCircle, label: 'Confirmed', variant: 'secondary' as const, color: 'text-blue-600' };\n      case 'approved':\n        return { icon: CheckCircle, label: 'Order Approved', variant: 'default' as const, color: 'text-green-600' };\n      case 'completed':\n        return { icon: CheckCircle, label: 'Completed', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'declined':\n        return { icon: XCircle, label: 'Declined', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  } else {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'responded':\n        return { icon: MessageSquare, label: 'Responded', variant: 'secondary' as const, color: 'text-blue-600' };\n      case 'in-progress':\n        return { icon: PlayCircle, label: 'In Progress', variant: 'default' as const, color: 'text-blue-600' };\n      case 'resolved':\n        return { icon: CheckCircle, label: 'Resolved', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'declined':\n        return { icon: XCircle, label: 'Declined', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  }\n};\n\nconst getAvailableStatuses = (currentStatus: string, type: 'custom-request' | 'contact-message' | 'purchase-request') => {\n  if (type === 'custom-request') {\n    switch (currentStatus) {\n      case 'pending':\n        return [\n          { value: 'in-progress', label: 'Accept & Start', icon: PlayCircle },\n          { value: 'cancelled', label: 'Decline', icon: XCircle }\n        ];\n      case 'in-progress':\n        return [\n          { value: 'completed', label: 'Mark Complete', icon: CheckCircle }\n        ];\n      default:\n        return [];\n    }\n  } else if (type === 'purchase-request') {\n    switch (currentStatus) {\n      case 'pending':\n        return [\n          { value: 'confirmed', label: 'Confirm Order', icon: CheckCircle },\n          { value: 'declined', label: 'Decline', icon: XCircle }\n        ];\n      case 'confirmed':\n        return [\n          { value: 'approved', label: 'Approve Order', icon: CheckCircle },\n          { value: 'declined', label: 'Decline', icon: XCircle }\n        ];\n      case 'approved':\n        return [\n          { value: 'completed', label: 'Mark Complete', icon: CheckCircle }\n        ];\n      default:\n        return [];\n    }\n  } else {\n    const allStatuses = [\n      { value: 'responded', label: 'Respond', icon: MessageSquare },\n      { value: 'in-progress', label: 'In Progress', icon: PlayCircle },\n      { value: 'declined', label: 'Decline', icon: XCircle },\n      { value: 'resolved', label: 'Resolve', icon: CheckCircle }\n    ];\n\n    // Filter out current status and resolved/declined if already set\n    return allStatuses.filter(status => {\n      if (status.value === currentStatus) return false;\n      if ((currentStatus === 'resolved' || currentStatus === 'declined') &&\n          (status.value === 'resolved' || status.value === 'declined')) return false;\n      return true;\n    });\n  }\n};\n\nexport default function StatusDropdown({ currentStatus, onStatusChange, type, disabled = false }: StatusDropdownProps) {\n  const [isUpdating, setIsUpdating] = useState(false);\n  const statusConfig = getStatusConfig(currentStatus, type);\n  const availableStatuses = getAvailableStatuses(currentStatus, type);\n  const StatusIcon = statusConfig.icon;\n\n  const handleStatusChange = async (newStatus: string) => {\n    setIsUpdating(true);\n    try {\n      await onStatusChange(newStatus);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  if (availableStatuses.length === 0) {\n    return (\n      <Badge variant={statusConfig.variant} className=\"flex items-center gap-1\">\n        <StatusIcon className=\"h-3 w-3\" />\n        {statusConfig.label}\n      </Badge>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <Badge variant={statusConfig.variant} className=\"flex items-center gap-1\">\n        <StatusIcon className=\"h-3 w-3\" />\n        {statusConfig.label}\n      </Badge>\n      \n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            disabled={disabled || isUpdating}\n            className=\"h-8 px-2\"\n          >\n            {isUpdating ? (\n              <Loader2 className=\"h-3 w-3 animate-spin\" />\n            ) : (\n              <>\n                <span className=\"sr-only sm:not-sr-only\">Update</span>\n                <ChevronDown className=\"h-3 w-3 ml-1\" />\n              </>\n            )}\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent align=\"end\" className=\"w-40\">\n          {availableStatuses.map((status) => {\n            const Icon = status.icon;\n            return (\n              <DropdownMenuItem\n                key={status.value}\n                onClick={() => handleStatusChange(status.value)}\n                className=\"flex items-center gap-2 cursor-pointer\"\n              >\n                <Icon className=\"h-4 w-4\" />\n                {status.label}\n              </DropdownMenuItem>\n            );\n          })}\n        </DropdownMenuContent>\n      </DropdownMenu>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;AA4BA,MAAM,kBAAkB,CAAC,QAAgB;IACvC,IAAI,SAAS,kBAAkB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,kNAAA,CAAA,aAAU;oBAAE,OAAO;oBAAe,SAAS;oBAAoB,OAAO;gBAAgB;YACvG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAiB;YACzG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAa,SAAS;oBAAwB,OAAO;gBAAe;YACrG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF,OAAO,IAAI,SAAS,oBAAoB;QACtC,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAgB;YACxG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAkB,SAAS;oBAAoB,OAAO;gBAAiB;YAC5G,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAiB;YACzG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAY,SAAS;oBAAwB,OAAO;gBAAe;YACpG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF,OAAO;QACL,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,wNAAA,CAAA,gBAAa;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAgB;YAC1G,KAAK;gBACH,OAAO;oBAAE,MAAM,kNAAA,CAAA,aAAU;oBAAE,OAAO;oBAAe,SAAS;oBAAoB,OAAO;gBAAgB;YACvG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAY,SAAS;oBAAsB,OAAO;gBAAiB;YACxG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAY,SAAS;oBAAwB,OAAO;gBAAe;YACpG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF;AACF;AAEA,MAAM,uBAAuB,CAAC,eAAuB;IACnD,IAAI,SAAS,kBAAkB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAe,OAAO;wBAAkB,MAAM,kNAAA,CAAA,aAAU;oBAAC;oBAClE;wBAAE,OAAO;wBAAa,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACvD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;iBACjE;YACH;gBACE,OAAO,EAAE;QACb;IACF,OAAO,IAAI,SAAS,oBAAoB;QACtC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;oBAChE;wBAAE,OAAO;wBAAY,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACtD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAY,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;oBAC/D;wBAAE,OAAO;wBAAY,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACtD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;iBACjE;YACH;gBACE,OAAO,EAAE;QACb;IACF,OAAO;QACL,MAAM,cAAc;YAClB;gBAAE,OAAO;gBAAa,OAAO;gBAAW,MAAM,wNAAA,CAAA,gBAAa;YAAC;YAC5D;gBAAE,OAAO;gBAAe,OAAO;gBAAe,MAAM,kNAAA,CAAA,aAAU;YAAC;YAC/D;gBAAE,OAAO;gBAAY,OAAO;gBAAW,MAAM,4MAAA,CAAA,UAAO;YAAC;YACrD;gBAAE,OAAO;gBAAY,OAAO;gBAAW,MAAM,2NAAA,CAAA,cAAW;YAAC;SAC1D;QAED,iEAAiE;QACjE,OAAO,YAAY,MAAM,CAAC,CAAA;YACxB,IAAI,OAAO,KAAK,KAAK,eAAe,OAAO;YAC3C,IAAI,CAAC,kBAAkB,cAAc,kBAAkB,UAAU,KAC7D,CAAC,OAAO,KAAK,KAAK,cAAc,OAAO,KAAK,KAAK,UAAU,GAAG,OAAO;YACzE,OAAO;QACT;IACF;AACF;AAEe,SAAS,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,IAAI,EAAE,WAAW,KAAK,EAAuB;IACnH,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,gBAAgB,eAAe;IACpD,MAAM,oBAAoB,qBAAqB,eAAe;IAC9D,MAAM,aAAa,aAAa,IAAI;IAEpC,MAAM,qBAAqB,OAAO;QAChC,cAAc;QACd,IAAI;YACF,MAAM,eAAe;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAClC,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAS,aAAa,OAAO;YAAE,WAAU;;8BAC9C,8OAAC;oBAAW,WAAU;;;;;;gBACrB,aAAa,KAAK;;;;;;;IAGzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,aAAa,OAAO;gBAAE,WAAU;;kCAC9C,8OAAC;wBAAW,WAAU;;;;;;oBACrB,aAAa,KAAK;;;;;;;0BAGrB,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,UAAU,YAAY;4BACtB,WAAU;sCAET,2BACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB;;kDACE,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;kDACzC,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAK/B,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAM,WAAU;kCACxC,kBAAkB,GAAG,CAAC,CAAC;4BACtB,MAAM,OAAO,OAAO,IAAI;4BACxB,qBACE,8OAAC,4IAAA,CAAA,mBAAgB;gCAEf,SAAS,IAAM,mBAAmB,OAAO,KAAK;gCAC9C,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;;;;;;oCACf,OAAO,KAAK;;+BALR,OAAO,KAAK;;;;;wBAQvB;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/purchase-requests/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport Link from 'next/link';\nimport {\n  ArrowLeft,\n  ShoppingCart,\n  Filter,\n  User,\n  Mail,\n  Phone,\n  Calendar,\n  MessageSquare\n} from 'lucide-react';\nimport { \n  getContactMessages, \n  updateContactMessageStatus,\n  subscribeToContactMessages \n} from '@/lib/firebaseServices';\nimport { ContactMessage } from '@/types';\nimport StatusDropdown from '@/components/admin/StatusDropdown';\n\nexport default function PurchaseRequestsPage() {\n  const { user, userData } = useAuth();\n  const [purchaseRequests, setPurchaseRequests] = useState<ContactMessage[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [selectedRequest, setSelectedRequest] = useState<ContactMessage | null>(null);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        const messages = await getContactMessages();\n        setPurchaseRequests(messages.filter(msg => msg.type === 'purchase-request'));\n      } catch (error: any) {\n        console.error('Error fetching purchase requests:', error);\n        setError('Failed to load purchase requests');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && userData?.role === 'admin') {\n      fetchData();\n\n      // Set up real-time listener\n      const unsubscribe = subscribeToContactMessages((messages) => {\n        setPurchaseRequests(messages.filter(msg => msg.type === 'purchase-request'));\n      });\n\n      return () => unsubscribe();\n    }\n  }, [user, userData]);\n\n  const handleUpdateStatus = async (requestId: string, status: ContactMessage['status']) => {\n    try {\n      await updateContactMessageStatus(requestId, status);\n      // The real-time listener will update the UI automatically\n    } catch (error: any) {\n      console.error('Error updating request status:', error);\n      setError('Failed to update request status');\n    }\n  };\n\n  const filteredRequests = purchaseRequests.filter(request => \n    statusFilter === 'all' || request.status === statusFilter\n  );\n\n  const getStatusCount = (status: string) => {\n    return purchaseRequests.filter(req => req.status === status).length;\n  };\n\n  if (!user || userData?.role !== 'admin') {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Access Denied</h1>\n        <p className=\"text-gray-600 mb-4\">You need admin privileges to access this page.</p>\n        <Button asChild>\n          <Link href=\"/dashboard\">Go to Dashboard</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <Button asChild variant=\"outline\" size=\"sm\">\n              <Link href=\"/admin\">\n                <ArrowLeft className=\"mr-2 h-4 w-4\" />\n                Back to Dashboard\n              </Link>\n            </Button>\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Purchase Requests\n          </h1>\n          <p className=\"text-gray-600\">\n            Manage template purchase requests from customers\n          </p>\n        </div>\n\n        {/* Error State */}\n        {error && (\n          <div className=\"mb-8 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-800\">{error}</p>\n          </div>\n        )}\n\n        {/* Stats Cards */}\n        {!loading && (\n          <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\">\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <ShoppingCart className=\"h-5 w-5 text-blue-600\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Total</p>\n                    <p className=\"text-xl font-bold\">{purchaseRequests.length}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-3 w-3 bg-yellow-500 rounded-full\"></div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n                    <p className=\"text-xl font-bold\">{getStatusCount('pending')}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-3 w-3 bg-blue-500 rounded-full\"></div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Confirmed</p>\n                    <p className=\"text-xl font-bold\">{getStatusCount('confirmed')}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-3 w-3 bg-green-500 rounded-full\"></div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Approved</p>\n                    <p className=\"text-xl font-bold\">{getStatusCount('approved')}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-3 w-3 bg-gray-500 rounded-full\"></div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Completed</p>\n                    <p className=\"text-xl font-bold\">{getStatusCount('completed')}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Filter Section */}\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle>Purchase Requests</CardTitle>\n                <CardDescription>\n                  Manage template purchase requests from customers\n                </CardDescription>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Filter className=\"h-4 w-4 text-gray-500\" />\n                <Select value={statusFilter} onValueChange={setStatusFilter}>\n                  <SelectTrigger className=\"w-40\">\n                    <SelectValue placeholder=\"Filter by status\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">All Status</SelectItem>\n                    <SelectItem value=\"pending\">Pending</SelectItem>\n                    <SelectItem value=\"confirmed\">Confirmed</SelectItem>\n                    <SelectItem value=\"approved\">Order Approved</SelectItem>\n                    <SelectItem value=\"completed\">Completed</SelectItem>\n                    <SelectItem value=\"declined\">Declined</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardHeader>\n          <CardContent>\n            {/* Loading State */}\n            {loading && (\n              <div className=\"flex justify-center items-center py-12\">\n                <div className=\"text-center\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                  <p className=\"text-gray-600\">Loading purchase requests...</p>\n                </div>\n              </div>\n            )}\n\n            {/* Requests List */}\n            {!loading && (\n              <div className=\"space-y-4\">\n                {filteredRequests.length > 0 ? (\n                  filteredRequests.map((request) => (\n                    <div \n                      key={request.id} \n                      className=\"p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors\"\n                      onClick={() => setSelectedRequest(request)}\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <h4 className=\"font-medium text-gray-900\">{request.userName}</h4>\n                            <Badge variant=\"default\" className=\"text-xs\">\n                              Purchase Request\n                            </Badge>\n                          </div>\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 mb-2\">\n                            <p className=\"text-sm text-gray-600 flex items-center gap-1\">\n                              <Mail className=\"h-3 w-3\" />\n                              {request.userEmail}\n                            </p>\n                            {request.userPhone && (\n                              <p className=\"text-sm text-gray-600 flex items-center gap-1\">\n                                <Phone className=\"h-3 w-3\" />\n                                {request.userPhone}\n                              </p>\n                            )}\n                          </div>\n                          <p className=\"text-sm font-medium text-gray-800 mt-1\">{request.subject}</p>\n                          <p className=\"text-sm text-gray-500 mt-2 line-clamp-2\">{request.message}</p>\n                          {request.templateTitle && (\n                            <p className=\"text-xs text-blue-600 mt-1\">Template: {request.templateTitle}</p>\n                          )}\n                          <p className=\"text-xs text-gray-400 mt-2 flex items-center gap-1\">\n                            <Calendar className=\"h-3 w-3\" />\n                            {new Date(request.createdAt.seconds ? request.createdAt.seconds * 1000 : request.createdAt).toLocaleDateString()}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center ml-4\" onClick={(e) => e.stopPropagation()}>\n                          <StatusDropdown\n                            currentStatus={request.status}\n                            onStatusChange={(status) => handleUpdateStatus(request.id, status)}\n                            type=\"purchase-request\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  ))\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <ShoppingCart className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No purchase requests</h3>\n                    <p className=\"text-gray-600\">\n                      {statusFilter === 'all' \n                        ? 'Purchase requests from customers will appear here.'\n                        : `No ${statusFilter} purchase requests found.`\n                      }\n                    </p>\n                  </div>\n                )}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Detailed Request View Dialog */}\n        {selectedRequest && (\n          <Dialog open={!!selectedRequest} onOpenChange={() => setSelectedRequest(null)}>\n            <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\n              <DialogHeader>\n                <DialogTitle>Purchase Request Details</DialogTitle>\n                <DialogDescription>\n                  Complete information about this purchase request\n                </DialogDescription>\n              </DialogHeader>\n              <div className=\"space-y-6\">\n                {/* User Information */}\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-semibold border-b pb-2\">Customer Information</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-600\">Name</label>\n                      <p className=\"text-sm text-gray-900\">{selectedRequest.userName}</p>\n                    </div>\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-600\">Email</label>\n                      <p className=\"text-sm text-gray-900\">{selectedRequest.userEmail}</p>\n                    </div>\n                    {selectedRequest.userPhone && (\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-600\">Phone</label>\n                        <p className=\"text-sm text-gray-900\">{selectedRequest.userPhone}</p>\n                      </div>\n                    )}\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-600\">Status</label>\n                      <div className=\"mt-1\">\n                        <StatusDropdown\n                          currentStatus={selectedRequest.status}\n                          onStatusChange={(status) => handleUpdateStatus(selectedRequest.id, status)}\n                          type=\"purchase-request\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Request Details */}\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-semibold border-b pb-2\">Request Details</h3>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-600\">Subject</label>\n                      <p className=\"text-sm text-gray-900\">{selectedRequest.subject}</p>\n                    </div>\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-600\">Message</label>\n                      <p className=\"text-sm text-gray-900 whitespace-pre-wrap\">{selectedRequest.message}</p>\n                    </div>\n                    {selectedRequest.templateTitle && (\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-600\">Template</label>\n                        <p className=\"text-sm text-blue-600\">{selectedRequest.templateTitle}</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Timeline */}\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-semibold border-b pb-2\">Timeline</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-600\">Created At</label>\n                      <p className=\"text-sm text-gray-900\">\n                        {new Date(selectedRequest.createdAt.seconds ? selectedRequest.createdAt.seconds * 1000 : selectedRequest.createdAt).toLocaleString()}\n                      </p>\n                    </div>\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-600\">Last Updated</label>\n                      <p className=\"text-sm text-gray-900\">\n                        {new Date(selectedRequest.updatedAt?.seconds ? selectedRequest.updatedAt.seconds * 1000 : selectedRequest.updatedAt || selectedRequest.createdAt).toLocaleString()}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </DialogContent>\n          </Dialog>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAMA;AA1BA;;;;;;;;;;;;;AA4Be,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAE9E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;gBACxC,oBAAoB,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YAC1D,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,QAAQ,UAAU,SAAS,SAAS;YACtC;YAEA,4BAA4B;YAC5B,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,CAAC;gBAC9C,oBAAoB,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YAC1D;YAEA,OAAO,IAAM;QACf;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;QAC5C,0DAA0D;QAC5D,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UAC/C,iBAAiB,SAAS,QAAQ,MAAM,KAAK;IAG/C,MAAM,iBAAiB,CAAC;QACtB,OAAO,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,QAAQ,MAAM;IACrE;IAEA,IAAI,CAAC,QAAQ,UAAU,SAAS,SAAS;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAa;;;;;;;;;;;;;;;;;IAIhC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,MAAK;0CACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAM9B,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;gBAKhC,CAAC,yBACA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAqB,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKjE,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAqB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKzD,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAqB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKzD,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAqB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKzD,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAqB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7D,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAc,eAAe;;kEAC1C,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMvC,8OAAC,gIAAA,CAAA,cAAW;;gCAET,yBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;gCAMlC,CAAC,yBACA,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,MAAM,GAAG,IACzB,iBAAiB,GAAG,CAAC,CAAC,wBACpB,8OAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,mBAAmB;sDAElC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAA6B,QAAQ,QAAQ;;;;;;kFAC3D,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAAU;;;;;;;;;;;;0EAI/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;;0FACX,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EACf,QAAQ,SAAS;;;;;;;oEAEnB,QAAQ,SAAS,kBAChB,8OAAC;wEAAE,WAAU;;0FACX,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,QAAQ,SAAS;;;;;;;;;;;;;0EAIxB,8OAAC;gEAAE,WAAU;0EAA0C,QAAQ,OAAO;;;;;;0EACtE,8OAAC;gEAAE,WAAU;0EAA2C,QAAQ,OAAO;;;;;;4DACtE,QAAQ,aAAa,kBACpB,8OAAC;gEAAE,WAAU;;oEAA6B;oEAAW,QAAQ,aAAa;;;;;;;0EAE5E,8OAAC;gEAAE,WAAU;;kFACX,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,IAAI,KAAK,QAAQ,SAAS,CAAC,OAAO,GAAG,QAAQ,SAAS,CAAC,OAAO,GAAG,OAAO,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;kEAGlH,8OAAC;wDAAI,WAAU;wDAAyB,SAAS,CAAC,IAAM,EAAE,eAAe;kEACvE,cAAA,8OAAC,6IAAA,CAAA,UAAc;4DACb,eAAe,QAAQ,MAAM;4DAC7B,gBAAgB,CAAC,SAAW,mBAAmB,QAAQ,EAAE,EAAE;4DAC3D,MAAK;;;;;;;;;;;;;;;;;2CAtCN,QAAQ,EAAE;;;;kEA6CnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,QACd,uDACA,CAAC,GAAG,EAAE,aAAa,yBAAyB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAW9D,iCACC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAM,CAAC,CAAC;oBAAiB,cAAc,IAAM,mBAAmB;8BACtE,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,cAAW;kDAAC;;;;;;kDACb,8OAAC,kIAAA,CAAA,oBAAiB;kDAAC;;;;;;;;;;;;0CAIrB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAAyB,gBAAgB,QAAQ;;;;;;;;;;;;kEAEhE,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAAyB,gBAAgB,SAAS;;;;;;;;;;;;oDAEhE,gBAAgB,SAAS,kBACxB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAAyB,gBAAgB,SAAS;;;;;;;;;;;;kEAGnE,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,6IAAA,CAAA,UAAc;oEACb,eAAe,gBAAgB,MAAM;oEACrC,gBAAgB,CAAC,SAAW,mBAAmB,gBAAgB,EAAE,EAAE;oEACnE,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAAyB,gBAAgB,OAAO;;;;;;;;;;;;kEAE/D,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAA6C,gBAAgB,OAAO;;;;;;;;;;;;oDAElF,gBAAgB,aAAa,kBAC5B,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAAyB,gBAAgB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;kDAO3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,gBAAgB,SAAS,CAAC,OAAO,GAAG,gBAAgB,SAAS,CAAC,OAAO,GAAG,OAAO,gBAAgB,SAAS,EAAE,cAAc;;;;;;;;;;;;kEAGtI,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,gBAAgB,SAAS,EAAE,UAAU,gBAAgB,SAAS,CAAC,OAAO,GAAG,OAAO,gBAAgB,SAAS,IAAI,gBAAgB,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxL", "debugId": null}}]}