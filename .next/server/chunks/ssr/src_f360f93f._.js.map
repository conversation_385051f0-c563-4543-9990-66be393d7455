{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  query,\n  orderBy,\n  onSnapshot\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    // Filter out undefined values to prevent Firebase errors\n    const cleanedData = Object.fromEntries(\n      Object.entries(requestData).filter(([_, value]) => value !== undefined)\n    );\n\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...cleanedData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    // Set default payment status when completing a request\n    if (status === 'completed') {\n      updateData.paymentStatus = 'pending';\n    }\n\n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestPaymentStatus = async (requestId: string, paymentStatus: CustomRequest['paymentStatus']) => {\n  try {\n    const updateData: any = {\n      paymentStatus,\n      updatedAt: new Date()\n    };\n\n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request payment status:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'contactMessages'), {\n      ...messageData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating contact message:', error);\n    throw error;\n  }\n};\n\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    await updateDoc(doc(db, 'contactMessages', messageId), updateData);\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n\nexport const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {\n  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const messages = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n    callback(messages);\n  });\n};\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAWA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,yDAAyD;QACzD,MAAM,cAAc,OAAO,WAAW,CACpC,OAAO,OAAO,CAAC,aAAa,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU;QAG/D,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,uDAAuD;QACvD,IAAI,WAAW,aAAa;YAC1B,WAAW,aAAa,GAAG;QAC7B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,mCAAmC,OAAO,WAAmB;IACxE,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB;YAC7D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB,QAAkC;IACpG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,CAAC;IACzC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAExE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/orders/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport Link from 'next/link';\nimport {\n  ArrowLeft,\n  Package,\n  Calendar,\n  IndianRupee,\n  Download,\n  ExternalLink,\n  Clock,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  Mail,\n  Phone,\n  User\n} from 'lucide-react';\nimport {\n  getContactMessages,\n  subscribeToContactMessages,\n  getCustomRequests,\n  subscribeToCustomRequests\n} from '@/lib/firebaseServices';\nimport { ContactMessage, CustomRequest } from '@/types';\n\nconst getStatusIcon = (status: string) => {\n  switch (status) {\n    case 'completed':\n      return <CheckCircle className=\"h-4 w-4 text-green-600\" />;\n    case 'approved':\n      return <CheckCircle className=\"h-4 w-4 text-blue-600\" />;\n    case 'confirmed':\n      return <Clock className=\"h-4 w-4 text-blue-600\" />;\n    case 'pending':\n      return <AlertCircle className=\"h-4 w-4 text-yellow-600\" />;\n    case 'declined':\n      return <XCircle className=\"h-4 w-4 text-red-600\" />;\n    default:\n      return <Package className=\"h-4 w-4 text-gray-600\" />;\n  }\n};\n\nconst getStatusColor = (status: string) => {\n  switch (status) {\n    case 'completed':\n      return 'bg-green-100 text-green-800';\n    case 'approved':\n      return 'bg-blue-100 text-blue-800';\n    case 'confirmed':\n      return 'bg-blue-100 text-blue-800';\n    case 'pending':\n      return 'bg-yellow-100 text-yellow-800';\n    case 'declined':\n      return 'bg-red-100 text-red-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nconst getStatusText = (status: string) => {\n  switch (status) {\n    case 'pending':\n      return 'Pending Review';\n    case 'confirmed':\n      return 'Confirmed';\n    case 'approved':\n      return 'Order Approved';\n    case 'completed':\n      return 'Completed';\n    case 'declined':\n      return 'Declined';\n    default:\n      return status.charAt(0).toUpperCase() + status.slice(1);\n  }\n};\n\nexport default function OrdersPage() {\n  const { user, userData } = useAuth();\n  const [purchaseRequests, setPurchaseRequests] = useState<ContactMessage[]>([]);\n  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user) return;\n\n      try {\n        setLoading(true);\n\n        // Fetch purchase requests\n        const messages = await getContactMessages();\n        const userPurchaseRequests = messages.filter(msg =>\n          msg.type === 'purchase-request' && msg.userEmail === user.email\n        );\n        setPurchaseRequests(userPurchaseRequests);\n\n        // Fetch custom requests\n        const customReqs = await getCustomRequests();\n        const userCustomRequests = customReqs.filter(req => req.userEmail === user.email);\n        setCustomRequests(userCustomRequests);\n\n      } catch (error: any) {\n        console.error('Error fetching orders:', error);\n        setError('Failed to load your orders');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user) {\n      fetchData();\n\n      // Set up real-time listeners\n      const unsubscribeMessages = subscribeToContactMessages((messages) => {\n        const userPurchaseRequests = messages.filter(msg =>\n          msg.type === 'purchase-request' && msg.userEmail === user.email\n        );\n        setPurchaseRequests(userPurchaseRequests);\n      });\n\n      const unsubscribeCustom = subscribeToCustomRequests((customReqs) => {\n        const userCustomRequests = customReqs.filter(req => req.userEmail === user.email);\n        setCustomRequests(userCustomRequests);\n      });\n\n      return () => {\n        unsubscribeMessages();\n        unsubscribeCustom();\n      };\n    }\n  }, [user]);\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Please Sign In</h1>\n        <p className=\"text-gray-600 mb-4\">You need to be signed in to view your orders.</p>\n        <Button asChild>\n          <Link href=\"/auth\">Sign In</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <Button asChild variant=\"outline\" size=\"sm\">\n              <Link href=\"/dashboard\">\n                <ArrowLeft className=\"mr-2 h-4 w-4\" />\n                Back to Dashboard\n              </Link>\n            </Button>\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            My Orders & Requests\n          </h1>\n          <p className=\"text-gray-600\">\n            Track your template purchases and custom design requests\n          </p>\n        </div>\n\n        {/* Error State */}\n        {error && (\n          <div className=\"mb-8 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-800\">{error}</p>\n          </div>\n        )}\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"text-center\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600\">Loading your orders...</p>\n            </div>\n          </div>\n        )}\n\n        {/* Orders List */}\n        {!loading && (\n          <div className=\"space-y-8\">\n            {/* Purchase Requests Section */}\n            <div>\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Template Purchase Requests</h2>\n              {purchaseRequests.length > 0 ? (\n              purchaseRequests.map((request) => (\n                <Card key={request.id} className=\"overflow-hidden\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex flex-col lg:flex-row lg:items-start gap-6\">\n                      {/* Request Details */}\n                      <div className=\"flex-1 space-y-4\">\n                        <div className=\"flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2\">\n                          <div>\n                            <h3 className=\"text-lg font-semibold text-gray-900\">\n                              {request.subject}\n                            </h3>\n                            {request.templateTitle && (\n                              <p className=\"text-sm text-blue-600 mt-1\">\n                                Template: {request.templateTitle}\n                              </p>\n                            )}\n                            <div className=\"flex items-center gap-2 mt-2\">\n                              {getStatusIcon(request.status)}\n                              <Badge className={getStatusColor(request.status)}>\n                                {getStatusText(request.status)}\n                              </Badge>\n                            </div>\n                          </div>\n                        </div>\n\n                        <div className=\"bg-gray-50 p-4 rounded-lg\">\n                          <h4 className=\"font-medium text-gray-900 mb-2\">Request Message:</h4>\n                          <p className=\"text-sm text-gray-700 whitespace-pre-wrap\">{request.message}</p>\n                        </div>\n\n                        <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                          <div className=\"flex items-center gap-1\">\n                            <Calendar className=\"h-4 w-4\" />\n                            Submitted on {new Date(request.createdAt.seconds ? request.createdAt.seconds * 1000 : request.createdAt).toLocaleDateString()}\n                          </div>\n                          <div className=\"flex items-center gap-1\">\n                            <Package className=\"h-4 w-4\" />\n                            Request #{request.id.slice(-6)}\n                          </div>\n                        </div>\n\n                        {/* Status Information */}\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <h4 className=\"font-medium text-blue-900 mb-2\">Status Information:</h4>\n                          <div className=\"text-sm text-blue-800\">\n                            {request.status === 'pending' && (\n                              <p>Your purchase request is being reviewed by our team. We'll contact you soon with more details.</p>\n                            )}\n                            {request.status === 'confirmed' && (\n                              <p>Your request has been confirmed! We're preparing your order details and will send you payment information shortly.</p>\n                            )}\n                            {request.status === 'approved' && (\n                              <p>Your order has been approved! Please proceed with the payment to complete your purchase.</p>\n                            )}\n                            {request.status === 'completed' && (\n                              <p>Your order is complete! You should have received the template files and documentation.</p>\n                            )}\n                            {request.status === 'declined' && (\n                              <p>Unfortunately, we couldn't process your request at this time. Please contact us for more information.</p>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Action Buttons */}\n                        <div className=\"flex flex-wrap gap-2 pt-2\">\n                          {request.status === 'pending' && (\n                            <Button size=\"sm\" variant=\"outline\" disabled>\n                              <Clock className=\"mr-2 h-4 w-4\" />\n                              Awaiting Review\n                            </Button>\n                          )}\n                          {request.status === 'confirmed' && (\n                            <Button size=\"sm\" variant=\"outline\" disabled>\n                              <AlertCircle className=\"mr-2 h-4 w-4\" />\n                              Preparing Order Details\n                            </Button>\n                          )}\n                          {request.status === 'approved' && (\n                            <Button size=\"sm\" variant=\"default\">\n                              <IndianRupee className=\"mr-2 h-4 w-4\" />\n                              Proceed to Payment\n                            </Button>\n                          )}\n                          {request.status === 'completed' && (\n                            <Button size=\"sm\" variant=\"default\">\n                              <Download className=\"mr-2 h-4 w-4\" />\n                              Download Files\n                            </Button>\n                          )}\n                          <Button size=\"sm\" variant=\"outline\" asChild>\n                            <Link href=\"/contact\">\n                              <Mail className=\"mr-2 h-4 w-4\" />\n                              Contact Support\n                            </Link>\n                          </Button>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))\n              ) : (\n                <Card>\n                  <CardContent className=\"text-center py-8\">\n                    <Package className=\"h-8 w-8 text-gray-300 mx-auto mb-2\" />\n                    <p className=\"text-gray-600\">No template purchase requests yet.</p>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Custom Requests Section */}\n            <div>\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Custom Design Requests</h2>\n              {customRequests.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {customRequests.map((request) => (\n                    <Card key={request.id} className=\"overflow-hidden\">\n                      <CardContent className=\"p-6\">\n                        <div className=\"flex flex-col lg:flex-row lg:items-start gap-6\">\n                          <div className=\"flex-1 space-y-4\">\n                            <div className=\"flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2\">\n                              <div>\n                                <h3 className=\"text-lg font-semibold text-gray-900\">\n                                  {request.title}\n                                </h3>\n                                <p className=\"text-sm text-purple-600 mt-1\">\n                                  Category: {request.category}\n                                </p>\n                                <div className=\"flex items-center gap-2 mt-2\">\n                                  {getStatusIcon(request.status)}\n                                  <Badge className={getStatusColor(request.status)}>\n                                    {getStatusText(request.status)}\n                                  </Badge>\n                                  {request.paymentStatus && request.status === 'completed' && (\n                                    <Badge className={request.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>\n                                      {request.paymentStatus === 'paid' ? 'Payment Done' : 'Payment Pending'}\n                                    </Badge>\n                                  )}\n                                </div>\n                              </div>\n                            </div>\n\n                            <div className=\"bg-gray-50 p-4 rounded-lg\">\n                              <h4 className=\"font-medium text-gray-900 mb-2\">Project Description:</h4>\n                              <p className=\"text-sm text-gray-700 whitespace-pre-wrap\">{request.description}</p>\n                              {request.budget && (\n                                <p className=\"text-sm text-green-600 mt-2 font-medium\">\n                                  Budget: ₹{request.budget.toLocaleString()}\n                                </p>\n                              )}\n                            </div>\n\n                            <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                              <div className=\"flex items-center gap-1\">\n                                <Calendar className=\"h-4 w-4\" />\n                                Submitted on {new Date(request.createdAt.seconds ? request.createdAt.seconds * 1000 : request.createdAt).toLocaleDateString()}\n                              </div>\n                              <div className=\"flex items-center gap-1\">\n                                <Package className=\"h-4 w-4\" />\n                                Request #{request.id.slice(-6)}\n                              </div>\n                            </div>\n\n                            {request.adminNotes && (\n                              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                                <h4 className=\"font-medium text-blue-900 mb-2\">Admin Notes:</h4>\n                                <p className=\"text-sm text-blue-800\">{request.adminNotes}</p>\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </div>\n              ) : (\n                <Card>\n                  <CardContent className=\"text-center py-8\">\n                    <Package className=\"h-8 w-8 text-gray-300 mx-auto mb-2\" />\n                    <p className=\"text-gray-600\">No custom design requests yet.</p>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Empty State for Both */}\n            {purchaseRequests.length === 0 && customRequests.length === 0 && (\n              <Card>\n                <CardContent className=\"text-center py-12\">\n                  <Package className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No orders yet</h3>\n                  <p className=\"text-gray-600 mb-4\">\n                    You haven't made any requests yet. Start by browsing templates or requesting a custom design.\n                  </p>\n                  <div className=\"flex flex-col sm:flex-row gap-2 justify-center\">\n                    <Button asChild>\n                      <Link href=\"/templates\">Browse Templates</Link>\n                    </Button>\n                    <Button asChild variant=\"outline\">\n                      <Link href=\"/custom-request\">Request Custom Design</Link>\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n        )}\n\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAxBA;;;;;;;;;;AAgCA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;YACE,qBAAO,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;IAC9B;AACF;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;IACzD;AACF;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI,CAAC,MAAM;YAEX,IAAI;gBACF,WAAW;gBAEX,0BAA0B;gBAC1B,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;gBACxC,MAAM,uBAAuB,SAAS,MAAM,CAAC,CAAA,MAC3C,IAAI,IAAI,KAAK,sBAAsB,IAAI,SAAS,KAAK,KAAK,KAAK;gBAEjE,oBAAoB;gBAEpB,wBAAwB;gBACxB,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;gBACzC,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,KAAK,KAAK,KAAK;gBAChF,kBAAkB;YAEpB,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,MAAM;YACR;YAEA,6BAA6B;YAC7B,MAAM,sBAAsB,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,CAAC;gBACtD,MAAM,uBAAuB,SAAS,MAAM,CAAC,CAAA,MAC3C,IAAI,IAAI,KAAK,sBAAsB,IAAI,SAAS,KAAK,KAAK,KAAK;gBAEjE,oBAAoB;YACtB;YAEA,MAAM,oBAAoB,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE,CAAC;gBACnD,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,KAAK,KAAK,KAAK;gBAChF,kBAAkB;YACpB;YAEA,OAAO;gBACL;gBACA;YACF;QACF;IACF,GAAG;QAAC;KAAK;IAET,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAQ;;;;;;;;;;;;;;;;;IAI3B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,MAAK;0CACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAM9B,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;gBAKhC,yBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;gBAMlC,CAAC,yBACA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;gCACxD,iBAAiB,MAAM,GAAG,IAC3B,iBAAiB,GAAG,CAAC,CAAC,wBACpB,8OAAC,gIAAA,CAAA,OAAI;wCAAkB,WAAU;kDAC/B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;0DAEb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFACX,QAAQ,OAAO;;;;;;oEAEjB,QAAQ,aAAa,kBACpB,8OAAC;wEAAE,WAAU;;4EAA6B;4EAC7B,QAAQ,aAAa;;;;;;;kFAGpC,8OAAC;wEAAI,WAAU;;4EACZ,cAAc,QAAQ,MAAM;0FAC7B,8OAAC,iIAAA,CAAA,QAAK;gFAAC,WAAW,eAAe,QAAQ,MAAM;0FAC5C,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;sEAMrC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAiC;;;;;;8EAC/C,8OAAC;oEAAE,WAAU;8EAA6C,QAAQ,OAAO;;;;;;;;;;;;sEAG3E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAY;wEAClB,IAAI,KAAK,QAAQ,SAAS,CAAC,OAAO,GAAG,QAAQ,SAAS,CAAC,OAAO,GAAG,OAAO,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;8EAE7H,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,wMAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAY;wEACrB,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;sEAKhC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAiC;;;;;;8EAC/C,8OAAC;oEAAI,WAAU;;wEACZ,QAAQ,MAAM,KAAK,2BAClB,8OAAC;sFAAE;;;;;;wEAEJ,QAAQ,MAAM,KAAK,6BAClB,8OAAC;sFAAE;;;;;;wEAEJ,QAAQ,MAAM,KAAK,4BAClB,8OAAC;sFAAE;;;;;;wEAEJ,QAAQ,MAAM,KAAK,6BAClB,8OAAC;sFAAE;;;;;;wEAEJ,QAAQ,MAAM,KAAK,4BAClB,8OAAC;sFAAE;;;;;;;;;;;;;;;;;;sEAMT,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,MAAM,KAAK,2BAClB,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAU,QAAQ;;sFAC1C,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAIrC,QAAQ,MAAM,KAAK,6BAClB,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAU,QAAQ;;sFAC1C,8OAAC,oNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAI3C,QAAQ,MAAM,KAAK,4BAClB,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;;sFACxB,8OAAC,oNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAI3C,QAAQ,MAAM,KAAK,6BAClB,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;;sFACxB,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAIzC,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAU,OAAO;8EACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;;0FACT,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA1FpC,QAAQ,EAAE;;;;8DAqGrB,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;gCACxD,eAAe,MAAM,GAAG,kBACvB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC,gIAAA,CAAA,OAAI;4CAAkB,WAAU;sDAC/B,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFACX,QAAQ,KAAK;;;;;;sFAEhB,8OAAC;4EAAE,WAAU;;gFAA+B;gFAC/B,QAAQ,QAAQ;;;;;;;sFAE7B,8OAAC;4EAAI,WAAU;;gFACZ,cAAc,QAAQ,MAAM;8FAC7B,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAW,eAAe,QAAQ,MAAM;8FAC5C,cAAc,QAAQ,MAAM;;;;;;gFAE9B,QAAQ,aAAa,IAAI,QAAQ,MAAM,KAAK,6BAC3C,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAW,QAAQ,aAAa,KAAK,SAAS,gCAAgC;8FAClF,QAAQ,aAAa,KAAK,SAAS,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;0EAO/D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAiC;;;;;;kFAC/C,8OAAC;wEAAE,WAAU;kFAA6C,QAAQ,WAAW;;;;;;oEAC5E,QAAQ,MAAM,kBACb,8OAAC;wEAAE,WAAU;;4EAA0C;4EAC3C,QAAQ,MAAM,CAAC,cAAc;;;;;;;;;;;;;0EAK7C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAY;4EAClB,IAAI,KAAK,QAAQ,SAAS,CAAC,OAAO,GAAG,QAAQ,SAAS,CAAC,OAAO,GAAG,OAAO,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;kFAE7H,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,wMAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAAY;4EACrB,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;4DAI/B,QAAQ,UAAU,kBACjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAiC;;;;;;kFAC/C,8OAAC;wEAAE,WAAU;kFAAyB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAlDzD,QAAQ,EAAE;;;;;;;;;yDA4DzB,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;wBAOpC,iBAAiB,MAAM,KAAK,KAAK,eAAe,MAAM,KAAK,mBAC1D,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAa;;;;;;;;;;;0DAE1B,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;0DACtB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnD", "debugId": null}}]}