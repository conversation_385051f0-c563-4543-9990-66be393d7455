{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  query,\n  orderBy,\n  onSnapshot\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    // Filter out undefined values to prevent Firebase errors\n    const cleanedData = Object.fromEntries(\n      Object.entries(requestData).filter(([_, value]) => value !== undefined)\n    );\n\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...cleanedData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    // Set default payment status when completing a request\n    if (status === 'completed') {\n      updateData.paymentStatus = 'pending';\n    }\n\n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestPaymentStatus = async (requestId: string, paymentStatus: CustomRequest['paymentStatus']) => {\n  try {\n    const updateData: any = {\n      paymentStatus,\n      updatedAt: new Date()\n    };\n\n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request payment status:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'contactMessages'), {\n      ...messageData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating contact message:', error);\n    throw error;\n  }\n};\n\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    await updateDoc(doc(db, 'contactMessages', messageId), updateData);\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n\nexport const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {\n  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const messages = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n    callback(messages);\n  });\n};\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAWA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,yDAAyD;QACzD,MAAM,cAAc,OAAO,WAAW,CACpC,OAAO,OAAO,CAAC,aAAa,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU;QAG/D,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,uDAAuD;QACvD,IAAI,WAAW,aAAa;YAC1B,WAAW,aAAa,GAAG;QAC7B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,mCAAmC,OAAO,WAAmB;IACxE,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB;YAC7D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB,QAAkC;IACpG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,CAAC;IACzC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAExE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/StatusDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  ChevronDown,\n  Clock,\n  PlayCircle,\n  CheckCircle,\n  XCircle,\n  MessageSquare,\n  Loader2,\n  CreditCard,\n  DollarSign\n} from 'lucide-react';\n\ninterface StatusDropdownProps {\n  currentStatus: string;\n  onStatusChange: (status: string) => Promise<void>;\n  type: 'custom-request' | 'contact-message' | 'purchase-request';\n  disabled?: boolean;\n  paymentStatus?: string;\n  onPaymentStatusChange?: (status: string) => Promise<void>;\n}\n\nconst getStatusConfig = (status: string, type: 'custom-request' | 'contact-message' | 'purchase-request') => {\n  if (type === 'custom-request') {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'in-progress':\n        return { icon: PlayCircle, label: 'In Progress', variant: 'default' as const, color: 'text-blue-600' };\n      case 'completed':\n        return { icon: CheckCircle, label: 'Completed', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'cancelled':\n        return { icon: XCircle, label: 'Cancelled', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  } else if (type === 'purchase-request') {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'confirmed':\n        return { icon: CheckCircle, label: 'Confirmed', variant: 'secondary' as const, color: 'text-blue-600' };\n      case 'approved':\n        return { icon: CheckCircle, label: 'Order Approved', variant: 'default' as const, color: 'text-green-600' };\n      case 'completed':\n        return { icon: CheckCircle, label: 'Completed', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'declined':\n        return { icon: XCircle, label: 'Declined', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  } else {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'responded':\n        return { icon: MessageSquare, label: 'Responded', variant: 'secondary' as const, color: 'text-blue-600' };\n      case 'in-progress':\n        return { icon: PlayCircle, label: 'In Progress', variant: 'default' as const, color: 'text-blue-600' };\n      case 'resolved':\n        return { icon: CheckCircle, label: 'Resolved', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'declined':\n        return { icon: XCircle, label: 'Declined', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  }\n};\n\nconst getPaymentStatusConfig = (paymentStatus: string) => {\n  switch (paymentStatus) {\n    case 'pending':\n      return { icon: Clock, label: 'Payment Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    case 'paid':\n      return { icon: CheckCircle, label: 'Payment Done', variant: 'secondary' as const, color: 'text-green-600' };\n    case 'not-required':\n      return { icon: DollarSign, label: 'No Payment Required', variant: 'default' as const, color: 'text-gray-600' };\n    default:\n      return { icon: Clock, label: 'Payment Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n  }\n};\n\nconst getAvailableStatuses = (currentStatus: string, type: 'custom-request' | 'contact-message' | 'purchase-request') => {\n  if (type === 'custom-request') {\n    switch (currentStatus) {\n      case 'pending':\n        return [\n          { value: 'in-progress', label: 'Accept & Start', icon: PlayCircle },\n          { value: 'cancelled', label: 'Decline', icon: XCircle }\n        ];\n      case 'in-progress':\n        return [\n          { value: 'completed', label: 'Mark Complete', icon: CheckCircle }\n        ];\n      default:\n        return [];\n    }\n  } else if (type === 'purchase-request') {\n    switch (currentStatus) {\n      case 'pending':\n        return [\n          { value: 'confirmed', label: 'Confirm Order', icon: CheckCircle },\n          { value: 'declined', label: 'Decline', icon: XCircle }\n        ];\n      case 'confirmed':\n        return [\n          { value: 'approved', label: 'Approve Order', icon: CheckCircle },\n          { value: 'declined', label: 'Decline', icon: XCircle }\n        ];\n      case 'approved':\n        return [\n          { value: 'completed', label: 'Mark Complete', icon: CheckCircle }\n        ];\n      default:\n        return [];\n    }\n  } else {\n    const allStatuses = [\n      { value: 'responded', label: 'Respond', icon: MessageSquare },\n      { value: 'in-progress', label: 'In Progress', icon: PlayCircle },\n      { value: 'declined', label: 'Decline', icon: XCircle },\n      { value: 'resolved', label: 'Resolve', icon: CheckCircle }\n    ];\n\n    // Filter out current status and resolved/declined if already set\n    return allStatuses.filter(status => {\n      if (status.value === currentStatus) return false;\n      if ((currentStatus === 'resolved' || currentStatus === 'declined') &&\n          (status.value === 'resolved' || status.value === 'declined')) return false;\n      return true;\n    });\n  }\n};\n\nexport default function StatusDropdown({\n  currentStatus,\n  onStatusChange,\n  type,\n  disabled = false,\n  paymentStatus,\n  onPaymentStatusChange\n}: StatusDropdownProps) {\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [isUpdatingPayment, setIsUpdatingPayment] = useState(false);\n  const statusConfig = getStatusConfig(currentStatus, type);\n  const availableStatuses = getAvailableStatuses(currentStatus, type);\n  const StatusIcon = statusConfig.icon;\n\n  const paymentStatusConfig = paymentStatus ? getPaymentStatusConfig(paymentStatus) : null;\n  const PaymentIcon = paymentStatusConfig?.icon;\n\n  const handleStatusChange = async (newStatus: string) => {\n    setIsUpdating(true);\n    try {\n      await onStatusChange(newStatus);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const handlePaymentStatusChange = async (newPaymentStatus: string) => {\n    if (!onPaymentStatusChange) return;\n\n    setIsUpdatingPayment(true);\n    try {\n      await onPaymentStatusChange(newPaymentStatus);\n    } catch (error) {\n      console.error('Error updating payment status:', error);\n    } finally {\n      setIsUpdatingPayment(false);\n    }\n  };\n\n  if (availableStatuses.length === 0) {\n    return (\n      <div className=\"flex items-center gap-2\">\n        <Badge variant={statusConfig.variant} className=\"flex items-center gap-1\">\n          <StatusIcon className=\"h-3 w-3\" />\n          {statusConfig.label}\n        </Badge>\n\n        {/* Show payment status for completed custom requests */}\n        {type === 'custom-request' && currentStatus === 'completed' && paymentStatusConfig && PaymentIcon && (\n          <div className=\"flex items-center gap-2\">\n            <Badge variant={paymentStatusConfig.variant} className=\"flex items-center gap-1\">\n              <PaymentIcon className=\"h-3 w-3\" />\n              {paymentStatusConfig.label}\n            </Badge>\n\n            {paymentStatus === 'pending' && onPaymentStatusChange && (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    disabled={disabled || isUpdatingPayment}\n                    className=\"h-8 px-2\"\n                  >\n                    {isUpdatingPayment ? (\n                      <Loader2 className=\"h-3 w-3 animate-spin\" />\n                    ) : (\n                      <>\n                        <span className=\"sr-only sm:not-sr-only\">Payment</span>\n                        <ChevronDown className=\"h-3 w-3 ml-1\" />\n                      </>\n                    )}\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent align=\"end\" className=\"w-40\">\n                  <DropdownMenuItem\n                    onClick={() => handlePaymentStatusChange('paid')}\n                    className=\"flex items-center gap-2 cursor-pointer\"\n                  >\n                    <CheckCircle className=\"h-4 w-4\" />\n                    Mark Paid\n                  </DropdownMenuItem>\n                  <DropdownMenuItem\n                    onClick={() => handlePaymentStatusChange('not-required')}\n                    className=\"flex items-center gap-2 cursor-pointer\"\n                  >\n                    <DollarSign className=\"h-4 w-4\" />\n                    No Payment Required\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            )}\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <Badge variant={statusConfig.variant} className=\"flex items-center gap-1\">\n        <StatusIcon className=\"h-3 w-3\" />\n        {statusConfig.label}\n      </Badge>\n      \n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            disabled={disabled || isUpdating}\n            className=\"h-8 px-2\"\n          >\n            {isUpdating ? (\n              <Loader2 className=\"h-3 w-3 animate-spin\" />\n            ) : (\n              <>\n                <span className=\"sr-only sm:not-sr-only\">Update</span>\n                <ChevronDown className=\"h-3 w-3 ml-1\" />\n              </>\n            )}\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent align=\"end\" className=\"w-40\">\n          {availableStatuses.map((status) => {\n            const Icon = status.icon;\n            return (\n              <DropdownMenuItem\n                key={status.value}\n                onClick={() => handleStatusChange(status.value)}\n                className=\"flex items-center gap-2 cursor-pointer\"\n              >\n                <Icon className=\"h-4 w-4\" />\n                {status.label}\n              </DropdownMenuItem>\n            );\n          })}\n        </DropdownMenuContent>\n      </DropdownMenu>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;AAgCA,MAAM,kBAAkB,CAAC,QAAgB;IACvC,IAAI,SAAS,kBAAkB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,kNAAA,CAAA,aAAU;oBAAE,OAAO;oBAAe,SAAS;oBAAoB,OAAO;gBAAgB;YACvG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAiB;YACzG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAa,SAAS;oBAAwB,OAAO;gBAAe;YACrG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF,OAAO,IAAI,SAAS,oBAAoB;QACtC,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAgB;YACxG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAkB,SAAS;oBAAoB,OAAO;gBAAiB;YAC5G,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAiB;YACzG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAY,SAAS;oBAAwB,OAAO;gBAAe;YACpG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF,OAAO;QACL,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,wNAAA,CAAA,gBAAa;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAgB;YAC1G,KAAK;gBACH,OAAO;oBAAE,MAAM,kNAAA,CAAA,aAAU;oBAAE,OAAO;oBAAe,SAAS;oBAAoB,OAAO;gBAAgB;YACvG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAY,SAAS;oBAAsB,OAAO;gBAAiB;YACxG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAY,SAAS;oBAAwB,OAAO;gBAAe;YACpG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF;AACF;AAEA,MAAM,yBAAyB,CAAC;IAC9B,OAAQ;QACN,KAAK;YACH,OAAO;gBAAE,MAAM,oMAAA,CAAA,QAAK;gBAAE,OAAO;gBAAmB,SAAS;gBAAoB,OAAO;YAAkB;QACxG,KAAK;YACH,OAAO;gBAAE,MAAM,2NAAA,CAAA,cAAW;gBAAE,OAAO;gBAAgB,SAAS;gBAAsB,OAAO;YAAiB;QAC5G,KAAK;YACH,OAAO;gBAAE,MAAM,kNAAA,CAAA,aAAU;gBAAE,OAAO;gBAAuB,SAAS;gBAAoB,OAAO;YAAgB;QAC/G;YACE,OAAO;gBAAE,MAAM,oMAAA,CAAA,QAAK;gBAAE,OAAO;gBAAmB,SAAS;gBAAoB,OAAO;YAAkB;IAC1G;AACF;AAEA,MAAM,uBAAuB,CAAC,eAAuB;IACnD,IAAI,SAAS,kBAAkB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAe,OAAO;wBAAkB,MAAM,kNAAA,CAAA,aAAU;oBAAC;oBAClE;wBAAE,OAAO;wBAAa,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACvD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;iBACjE;YACH;gBACE,OAAO,EAAE;QACb;IACF,OAAO,IAAI,SAAS,oBAAoB;QACtC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;oBAChE;wBAAE,OAAO;wBAAY,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACtD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAY,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;oBAC/D;wBAAE,OAAO;wBAAY,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACtD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;iBACjE;YACH;gBACE,OAAO,EAAE;QACb;IACF,OAAO;QACL,MAAM,cAAc;YAClB;gBAAE,OAAO;gBAAa,OAAO;gBAAW,MAAM,wNAAA,CAAA,gBAAa;YAAC;YAC5D;gBAAE,OAAO;gBAAe,OAAO;gBAAe,MAAM,kNAAA,CAAA,aAAU;YAAC;YAC/D;gBAAE,OAAO;gBAAY,OAAO;gBAAW,MAAM,4MAAA,CAAA,UAAO;YAAC;YACrD;gBAAE,OAAO;gBAAY,OAAO;gBAAW,MAAM,2NAAA,CAAA,cAAW;YAAC;SAC1D;QAED,iEAAiE;QACjE,OAAO,YAAY,MAAM,CAAC,CAAA;YACxB,IAAI,OAAO,KAAK,KAAK,eAAe,OAAO;YAC3C,IAAI,CAAC,kBAAkB,cAAc,kBAAkB,UAAU,KAC7D,CAAC,OAAO,KAAK,KAAK,cAAc,OAAO,KAAK,KAAK,UAAU,GAAG,OAAO;YACzE,OAAO;QACT;IACF;AACF;AAEe,SAAS,eAAe,EACrC,aAAa,EACb,cAAc,EACd,IAAI,EACJ,WAAW,KAAK,EAChB,aAAa,EACb,qBAAqB,EACD;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,eAAe,gBAAgB,eAAe;IACpD,MAAM,oBAAoB,qBAAqB,eAAe;IAC9D,MAAM,aAAa,aAAa,IAAI;IAEpC,MAAM,sBAAsB,gBAAgB,uBAAuB,iBAAiB;IACpF,MAAM,cAAc,qBAAqB;IAEzC,MAAM,qBAAqB,OAAO;QAChC,cAAc;QACd,IAAI;YACF,MAAM,eAAe;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,4BAA4B,OAAO;QACvC,IAAI,CAAC,uBAAuB;QAE5B,qBAAqB;QACrB,IAAI;YACF,MAAM,sBAAsB;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAClC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAS,aAAa,OAAO;oBAAE,WAAU;;sCAC9C,8OAAC;4BAAW,WAAU;;;;;;wBACrB,aAAa,KAAK;;;;;;;gBAIpB,SAAS,oBAAoB,kBAAkB,eAAe,uBAAuB,6BACpF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS,oBAAoB,OAAO;4BAAE,WAAU;;8CACrD,8OAAC;oCAAY,WAAU;;;;;;gCACtB,oBAAoB,KAAK;;;;;;;wBAG3B,kBAAkB,aAAa,uCAC9B,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,UAAU,YAAY;wCACtB,WAAU;kDAET,kCACC,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB;;8DACE,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;8DACzC,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAK/B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,SAAS,IAAM,0BAA0B;4CACzC,WAAU;;8DAEV,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGrC,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,SAAS,IAAM,0BAA0B;4CACzC,WAAU;;8DAEV,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,aAAa,OAAO;gBAAE,WAAU;;kCAC9C,8OAAC;wBAAW,WAAU;;;;;;oBACrB,aAAa,KAAK;;;;;;;0BAGrB,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,UAAU,YAAY;4BACtB,WAAU;sCAET,2BACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB;;kDACE,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;kDACzC,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAK/B,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAM,WAAU;kCACxC,kBAAkB,GAAG,CAAC,CAAC;4BACtB,MAAM,OAAO,OAAO,IAAI;4BACxB,qBACE,8OAAC,4IAAA,CAAA,mBAAgB;gCAEf,SAAS,IAAM,mBAAmB,OAAO,KAAK;gCAC9C,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;;;;;;oCACf,OAAO,KAAK;;+BALR,OAAO,KAAK;;;;;wBAQvB;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/custom-requests/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport {\n  FileText,\n  Clock,\n  DollarSign,\n  User,\n  Calendar,\n  MessageSquare,\n  CheckCircle,\n  XCircle,\n  PlayCircle,\n  Loader2,\n  AlertCircle,\n  Filter,\n  Phone,\n  Mail,\n  Eye\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { getCustomRequests, updateCustomRequestStatus, updateCustomRequestPaymentStatus, getAllUsers } from '@/lib/firebaseServices';\nimport { CustomRequest, User } from '@/types';\nimport StatusDropdown from '@/components/admin/StatusDropdown';\n\nexport default function AdminCustomRequestsPage() {\n  const { user, userData } = useAuth();\n  const [requests, setRequests] = useState<CustomRequest[]>([]);\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedRequest, setSelectedRequest] = useState<CustomRequest | null>(null);\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [adminNotes, setAdminNotes] = useState('');\n  const [updating, setUpdating] = useState(false);\n  const [statusFilter, setStatusFilter] = useState('all');\n\n  useEffect(() => {\n    const fetchRequests = async () => {\n      try {\n        setLoading(true);\n        const [fetchedRequests, fetchedUsers] = await Promise.all([\n          getCustomRequests(),\n          getAllUsers()\n        ]);\n        setRequests(fetchedRequests);\n        setUsers(fetchedUsers);\n      } catch (error: any) {\n        console.error('Error fetching data:', error);\n        setError('Failed to load data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && userData?.role === 'admin') {\n      fetchRequests();\n    }\n  }, [user, userData]);\n\n  const handleUpdateStatus = async (requestId: string, status: CustomRequest['status']) => {\n    try {\n      setUpdating(true);\n      await updateCustomRequestStatus(requestId, status, adminNotes);\n\n      // Update local state\n      setRequests(prev => prev.map(req =>\n        req.id === requestId\n          ? {\n              ...req,\n              status,\n              adminNotes,\n              updatedAt: new Date(),\n              // Set payment status to pending when completing\n              ...(status === 'completed' && { paymentStatus: 'pending' })\n            }\n          : req\n      ));\n\n      setSelectedRequest(null);\n      setAdminNotes('');\n    } catch (error: any) {\n      console.error('Error updating request status:', error);\n      setError('Failed to update request status');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleUpdatePaymentStatus = async (requestId: string, paymentStatus: CustomRequest['paymentStatus']) => {\n    try {\n      setUpdating(true);\n      await updateCustomRequestPaymentStatus(requestId, paymentStatus);\n\n      // Update local state\n      setRequests(prev => prev.map(req =>\n        req.id === requestId\n          ? { ...req, paymentStatus, updatedAt: new Date() }\n          : req\n      ));\n    } catch (error: any) {\n      console.error('Error updating payment status:', error);\n      setError('Failed to update payment status');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleShowUserDetails = (userEmail: string) => {\n    const user = users.find(u => u.email === userEmail);\n    if (user) {\n      setSelectedUser(user);\n    }\n  };\n\n  if (!user || userData?.role !== 'admin') {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Access Denied</h1>\n        <p className=\"text-gray-600 mb-4\">You need admin privileges to access this page.</p>\n        <Button asChild>\n          <Link href=\"/dashboard\">Go to Dashboard</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  const getStatusIcon = (status: CustomRequest['status']) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"h-4 w-4\" />;\n      case 'in-progress':\n        return <PlayCircle className=\"h-4 w-4\" />;\n      case 'completed':\n        return <CheckCircle className=\"h-4 w-4\" />;\n      case 'cancelled':\n        return <XCircle className=\"h-4 w-4\" />;\n      default:\n        return <Clock className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getStatusColor = (status: CustomRequest['status']) => {\n    switch (status) {\n      case 'pending':\n        return 'outline';\n      case 'in-progress':\n        return 'secondary';\n      case 'completed':\n        return 'default';\n      case 'cancelled':\n        return 'destructive';\n      default:\n        return 'outline';\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Custom Requests Management\n        </h1>\n        <p className=\"text-gray-600\">\n          Manage and respond to custom design requests from users\n        </p>\n      </div>\n\n      {/* Loading State */}\n      {loading && (\n        <div className=\"flex justify-center items-center py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading custom requests...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <Alert variant=\"destructive\" className=\"mb-6\">\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      {/* Stats */}\n      {!loading && (\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"h-5 w-5 text-yellow-600\" />\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n                  <p className=\"text-xl font-bold\">\n                    {requests.filter(r => r.status === 'pending').length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <PlayCircle className=\"h-5 w-5 text-blue-600\" />\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">In Progress</p>\n                  <p className=\"text-xl font-bold\">\n                    {requests.filter(r => r.status === 'in-progress').length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Completed</p>\n                  <p className=\"text-xl font-bold\">\n                    {requests.filter(r => r.status === 'completed').length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <FileText className=\"h-5 w-5 text-gray-600\" />\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total</p>\n                  <p className=\"text-xl font-bold\">{requests.length}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Filter Section */}\n      {!loading && (\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Custom Design Requests</h2>\n          <div className=\"flex items-center gap-2\">\n            <Filter className=\"h-4 w-4 text-gray-500\" />\n            <Select value={statusFilter} onValueChange={setStatusFilter}>\n              <SelectTrigger className=\"w-40\">\n                <SelectValue placeholder=\"Filter by status\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Status</SelectItem>\n                <SelectItem value=\"pending\">Pending</SelectItem>\n                <SelectItem value=\"in-progress\">In Progress</SelectItem>\n                <SelectItem value=\"completed\">Completed</SelectItem>\n                <SelectItem value=\"cancelled\">Cancelled</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n      )}\n\n      {/* Requests List */}\n      {!loading && (\n        <div className=\"space-y-4\">\n          {requests.filter(request => statusFilter === 'all' || request.status === statusFilter).length > 0 ? (\n            requests.filter(request => statusFilter === 'all' || request.status === statusFilter).map((request) => (\n              <Card key={request.id}>\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-3 mb-3\">\n                        <h3 className=\"text-lg font-semibold text-gray-900\">\n                          {request.title}\n                        </h3>\n                        <Badge variant={getStatusColor(request.status)} className=\"flex items-center space-x-1\">\n                          {getStatusIcon(request.status)}\n                          <span>{request.status}</span>\n                        </Badge>\n                      </div>\n                      \n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                        <div className=\"flex items-center space-x-2 text-sm\">\n                          <User className=\"h-4 w-4 text-gray-600\" />\n                          <button\n                            onClick={() => handleShowUserDetails(request.userEmail)}\n                            className=\"text-blue-600 hover:text-blue-800 hover:underline cursor-pointer\"\n                          >\n                            {request.userEmail}\n                          </button>\n                        </div>\n                        <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                          <FileText className=\"h-4 w-4\" />\n                          <span>{request.category}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                          <Calendar className=\"h-4 w-4\" />\n                          <span>{new Date(request.createdAt).toLocaleDateString()}</span>\n                        </div>\n                      </div>\n                      \n                      {request.budget && (\n                        <div className=\"flex items-center space-x-2 text-sm text-gray-600 mb-3\">\n                          <DollarSign className=\"h-4 w-4\" />\n                          <span>Budget: ${request.budget}</span>\n                        </div>\n                      )}\n                      \n                      <p className=\"text-gray-700 mb-4\">{request.description}</p>\n                      \n                      {request.adminNotes && (\n                        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <MessageSquare className=\"h-4 w-4 text-blue-600\" />\n                            <span className=\"text-sm font-medium text-blue-800\">Admin Notes</span>\n                          </div>\n                          <p className=\"text-sm text-blue-700\">{request.adminNotes}</p>\n                        </div>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2 ml-4\">\n                      <StatusDropdown\n                        currentStatus={request.status}\n                        onStatusChange={(status) => handleUpdateStatus(request.id, status)}\n                        type=\"custom-request\"\n                        paymentStatus={request.paymentStatus}\n                        onPaymentStatusChange={(paymentStatus) => handleUpdatePaymentStatus(request.id, paymentStatus)}\n                      />\n                      {(request.status === 'pending' || request.status === 'in-progress') && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => setSelectedRequest(request)}\n                        >\n                          Add Notes\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))\n          ) : (\n            <Card>\n              <CardContent className=\"p-12 text-center\">\n                <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Custom Requests</h3>\n                <p className=\"text-gray-600\">No custom design requests have been submitted yet.</p>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      )}\n\n      {/* Request Management Modal */}\n      {selectedRequest && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <Card className=\"w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n            <CardHeader>\n              <CardTitle>Manage Request: {selectedRequest.title}</CardTitle>\n              <CardDescription>\n                Update the status and add admin notes for this request\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <Label htmlFor=\"adminNotes\">Admin Notes</Label>\n                <Textarea\n                  id=\"adminNotes\"\n                  placeholder=\"Add notes about this request...\"\n                  value={adminNotes}\n                  onChange={(e) => setAdminNotes(e.target.value)}\n                  rows={4}\n                />\n              </div>\n              \n              <div className=\"flex space-x-2\">\n                {selectedRequest.status === 'pending' && (\n                  <>\n                    <Button \n                      onClick={() => handleUpdateStatus(selectedRequest.id, 'in-progress')}\n                      disabled={updating}\n                    >\n                      {updating && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                      Accept & Start\n                    </Button>\n                    <Button \n                      variant=\"destructive\"\n                      onClick={() => handleUpdateStatus(selectedRequest.id, 'cancelled')}\n                      disabled={updating}\n                    >\n                      {updating && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                      Decline\n                    </Button>\n                  </>\n                )}\n                \n                {selectedRequest.status === 'in-progress' && (\n                  <Button \n                    onClick={() => handleUpdateStatus(selectedRequest.id, 'completed')}\n                    disabled={updating}\n                  >\n                    {updating && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                    Mark Complete\n                  </Button>\n                )}\n                \n                <Button \n                  variant=\"outline\" \n                  onClick={() => {\n                    setSelectedRequest(null);\n                    setAdminNotes('');\n                  }}\n                >\n                  Cancel\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* User Details Modal */}\n      <Dialog open={!!selectedUser} onOpenChange={() => setSelectedUser(null)}>\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle>User Details</DialogTitle>\n            <DialogDescription>\n              Complete information about the user who submitted this request\n            </DialogDescription>\n          </DialogHeader>\n          {selectedUser && (\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 gap-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <Mail className=\"h-4 w-4 text-gray-500\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Email</p>\n                    <p className=\"text-sm text-gray-600\">{selectedUser.email}</p>\n                  </div>\n                </div>\n\n                {selectedUser.fullName && (\n                  <div className=\"flex items-center space-x-3\">\n                    <User className=\"h-4 w-4 text-gray-500\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">Full Name</p>\n                      <p className=\"text-sm text-gray-600\">{selectedUser.fullName}</p>\n                    </div>\n                  </div>\n                )}\n\n                {selectedUser.phoneNumber && (\n                  <div className=\"flex items-center space-x-3\">\n                    <Phone className=\"h-4 w-4 text-gray-500\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">Phone Number</p>\n                      <p className=\"text-sm text-gray-600\">\n                        {selectedUser.countryCode} {selectedUser.phoneNumber}\n                      </p>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"flex items-center space-x-3\">\n                  <Calendar className=\"h-4 w-4 text-gray-500\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Member Since</p>\n                    <p className=\"text-sm text-gray-600\">\n                      {new Date(selectedUser.createdAt).toLocaleDateString()}\n                    </p>\n                  </div>\n                </div>\n\n                {selectedUser.purchasedTemplates && selectedUser.purchasedTemplates.length > 0 && (\n                  <div className=\"flex items-start space-x-3\">\n                    <FileText className=\"h-4 w-4 text-gray-500 mt-0.5\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">Purchased Templates</p>\n                      <p className=\"text-sm text-gray-600\">\n                        {selectedUser.purchasedTemplates.length} template(s)\n                      </p>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AAhCA;;;;;;;;;;;;;;;;;AAkCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,MAAM,CAAC,iBAAiB,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACxD,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;oBAChB,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;iBACX;gBACD,YAAY;gBACZ,SAAS;YACX,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,QAAQ,UAAU,SAAS,SAAS;YACtC;QACF;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI;YACF,YAAY;YACZ,MAAM,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW,QAAQ;YAEnD,qBAAqB;YACrB,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,YACP;wBACE,GAAG,GAAG;wBACN;wBACA;wBACA,WAAW,IAAI;wBACf,gDAAgD;wBAChD,GAAI,WAAW,eAAe;4BAAE,eAAe;wBAAU,CAAC;oBAC5D,IACA;YAGN,mBAAmB;YACnB,cAAc;QAChB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,4BAA4B,OAAO,WAAmB;QAC1D,IAAI;YACF,YAAY;YACZ,MAAM,CAAA,GAAA,8HAAA,CAAA,mCAAgC,AAAD,EAAE,WAAW;YAElD,qBAAqB;YACrB,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,YACP;wBAAE,GAAG,GAAG;wBAAE;wBAAe,WAAW,IAAI;oBAAO,IAC/C;QAER,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QACzC,IAAI,MAAM;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,UAAU,SAAS,SAAS;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAa;;;;;;;;;;;;;;;;;IAIhC;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAM9B,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMlC,uBACC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAc,WAAU;0BACrC,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;8BAAE;;;;;;;;;;;YAKtB,CAAC,yBACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9D,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqB,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS5D,CAAC,yBACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAc,eAAe;;kDAC1C,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAc;;;;;;0DAChC,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQvC,CAAC,yBACA,8OAAC;gBAAI,WAAU;0BACZ,SAAS,MAAM,CAAC,CAAA,UAAW,iBAAiB,SAAS,QAAQ,MAAM,KAAK,cAAc,MAAM,GAAG,IAC9F,SAAS,MAAM,CAAC,CAAA,UAAW,iBAAiB,SAAS,QAAQ,MAAM,KAAK,cAAc,GAAG,CAAC,CAAC,wBACzF,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,eAAe,QAAQ,MAAM;wDAAG,WAAU;;4DACvD,cAAc,QAAQ,MAAM;0EAC7B,8OAAC;0EAAM,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0DAIzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,qHAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,SAAS,IAAM,sBAAsB,QAAQ,SAAS;gEACtD,WAAU;0EAET,QAAQ,SAAS;;;;;;;;;;;;kEAGtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,QAAQ,QAAQ;;;;;;;;;;;;kEAEzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;4CAIxD,QAAQ,MAAM,kBACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;;4DAAK;4DAAU,QAAQ,MAAM;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;4CAErD,QAAQ,UAAU,kBACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EACzB,8OAAC;gEAAK,WAAU;0EAAoC;;;;;;;;;;;;kEAEtD,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;kDAK9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6IAAA,CAAA,UAAc;gDACb,eAAe,QAAQ,MAAM;gDAC7B,gBAAgB,CAAC,SAAW,mBAAmB,QAAQ,EAAE,EAAE;gDAC3D,MAAK;gDACL,eAAe,QAAQ,aAAa;gDACpC,uBAAuB,CAAC,gBAAkB,0BAA0B,QAAQ,EAAE,EAAE;;;;;;4CAEjF,CAAC,QAAQ,MAAM,KAAK,aAAa,QAAQ,MAAM,KAAK,aAAa,mBAChE,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,mBAAmB;0DACnC;;;;;;;;;;;;;;;;;;;;;;;uBAnEA,QAAQ,EAAE;;;;8CA6EvB,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;YAQtC,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;;wCAAC;wCAAiB,gBAAgB,KAAK;;;;;;;8CACjD,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,MAAM;;;;;;;;;;;;8CAIV,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,MAAM,KAAK,2BAC1B;;8DACE,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,mBAAmB,gBAAgB,EAAE,EAAE;oDACtD,UAAU;;wDAET,0BAAY,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;8DAGjE,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,mBAAmB,gBAAgB,EAAE,EAAE;oDACtD,UAAU;;wDAET,0BAAY,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;;;wCAMpE,gBAAgB,MAAM,KAAK,+BAC1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,mBAAmB,gBAAgB,EAAE,EAAE;4CACtD,UAAU;;gDAET,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;sDAKnE,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;gDACP,mBAAmB;gDACnB,cAAc;4CAChB;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM,CAAC,CAAC;gBAAc,cAAc,IAAM,gBAAgB;0BAChE,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;wBAIpB,8BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAyB,aAAa,KAAK;;;;;;;;;;;;;;;;;;oCAI3D,aAAa,QAAQ,kBACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,qHAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAyB,aAAa,QAAQ;;;;;;;;;;;;;;;;;;oCAKhE,aAAa,WAAW,kBACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;;4DACV,aAAa,WAAW;4DAAC;4DAAE,aAAa,WAAW;;;;;;;;;;;;;;;;;;;kDAM5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;oCAKzD,aAAa,kBAAkB,IAAI,aAAa,kBAAkB,CAAC,MAAM,GAAG,mBAC3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;;4DACV,aAAa,kBAAkB,CAAC,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhE", "debugId": null}}]}