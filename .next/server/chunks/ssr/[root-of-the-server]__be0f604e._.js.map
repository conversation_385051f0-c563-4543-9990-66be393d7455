{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport default app;\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;AACnB,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;uCAChB", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { \n  User, \n  signInWithEmailAndPassword, \n  createUserWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged\n} from 'firebase/auth';\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\nimport { auth, db } from '@/lib/firebase';\n\ninterface UserData {\n  uid: string;\n  email: string;\n  role: 'admin' | 'user';\n  displayName?: string;\n  fullName?: string;\n  phoneNumber?: string;\n  countryCode?: string;\n  createdAt: Date;\n  updatedAt?: Date;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  userData: UserData | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  updateUserProfile: (data: Partial<UserData>) => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [userData, setUserData] = useState<UserData | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      if (user) {\n        setUser(user);\n        try {\n          // Fetch user data from Firestore\n          const userDoc = await getDoc(doc(db, 'users', user.uid));\n          if (userDoc.exists()) {\n            setUserData(userDoc.data() as UserData);\n          } else {\n            // Create user document if it doesn't exist (for existing users)\n            const userData: UserData = {\n              uid: user.uid,\n              email: user.email!,\n              role: 'user',\n              displayName: user.displayName || '',\n              createdAt: new Date()\n            };\n            await setDoc(doc(db, 'users', user.uid), userData);\n            setUserData(userData);\n          }\n        } catch (error) {\n          console.error('Error fetching/creating user data:', error);\n          // Set basic user data even if Firestore fails\n          setUserData({\n            uid: user.uid,\n            email: user.email!,\n            role: 'user',\n            displayName: user.displayName || '',\n            createdAt: new Date()\n          });\n        }\n      } else {\n        setUser(null);\n        setUserData(null);\n      }\n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      await signInWithEmailAndPassword(auth, email, password);\n    } catch (error: any) {\n      console.error('Sign in error:', error);\n      throw error;\n    }\n  };\n\n  const signUp = async (email: string, password: string) => {\n    try {\n      const { user } = await createUserWithEmailAndPassword(auth, email, password);\n\n      // Create user document in Firestore\n      const userData: UserData = {\n        uid: user.uid,\n        email: user.email!,\n        role: 'user', // Default to user role\n        displayName: user.displayName || '',\n        createdAt: new Date()\n      };\n\n      await setDoc(doc(db, 'users', user.uid), userData);\n    } catch (error: any) {\n      console.error('Sign up error:', error);\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    await signOut(auth);\n  };\n\n  const updateUserProfile = async (data: Partial<UserData>) => {\n    if (!user) throw new Error('No user logged in');\n\n    try {\n      const updatedData = {\n        ...data,\n        updatedAt: new Date()\n      };\n\n      await setDoc(doc(db, 'users', user.uid), updatedData, { merge: true });\n\n      // Update local userData state\n      if (userData) {\n        setUserData({ ...userData, ...updatedData });\n      }\n    } catch (error: any) {\n      console.error('Error updating user profile:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    userData,\n    loading,\n    signIn,\n    signUp,\n    logout,\n    updateUserProfile\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;AACA;AAXA;;;;;;AAmCA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;YAClD,IAAI,MAAM;gBACR,QAAQ;gBACR,IAAI;oBACF,iCAAiC;oBACjC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG;oBACtD,IAAI,QAAQ,MAAM,IAAI;wBACpB,YAAY,QAAQ,IAAI;oBAC1B,OAAO;wBACL,gEAAgE;wBAChE,MAAM,WAAqB;4BACzB,KAAK,KAAK,GAAG;4BACb,OAAO,KAAK,KAAK;4BACjB,MAAM;4BACN,aAAa,KAAK,WAAW,IAAI;4BACjC,WAAW,IAAI;wBACjB;wBACA,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG,GAAG;wBACzC,YAAY;oBACd;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,8CAA8C;oBAC9C,YAAY;wBACV,KAAK,KAAK,GAAG;wBACb,OAAO,KAAK,KAAK;wBACjB,MAAM;wBACN,aAAa,KAAK,WAAW,IAAI;wBACjC,WAAW,IAAI;oBACjB;gBACF;YACF,OAAO;gBACL,QAAQ;gBACR,YAAY;YACd;YACA,WAAW;QACb;QAEA,OAAO,IAAM;IACf,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;QAChD,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;YAEnE,oCAAoC;YACpC,MAAM,WAAqB;gBACzB,KAAK,KAAK,GAAG;gBACb,OAAO,KAAK,KAAK;gBACjB,MAAM;gBACN,aAAa,KAAK,WAAW,IAAI;gBACjC,WAAW,IAAI;YACjB;YAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG,GAAG;QAC3C,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE,sHAAA,CAAA,OAAI;IACpB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,IAAI;YACF,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,WAAW,IAAI;YACjB;YAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG,GAAG,aAAa;gBAAE,OAAO;YAAK;YAEpE,8BAA8B;YAC9B,IAAI,UAAU;gBACZ,YAAY;oBAAE,GAAG,QAAQ;oBAAE,GAAG,WAAW;gBAAC;YAC5C;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/button';\nimport { \n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\nimport { Menu, User, LogOut, Settings, ShoppingBag, Plus, Home, FileText, Palette, Phone } from 'lucide-react';\n\nexport const Navbar = () => {\n  const { user, userData, logout } = useAuth();\n  const [isOpen, setIsOpen] = useState(false);\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  const NavLinks = () => (\n    <>\n      <Link href=\"/\" className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center\">\n        <Home className=\"w-4 h-4 mr-2\" />\n        Home\n      </Link>\n      <Link href=\"/templates\" className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center\">\n        <FileText className=\"w-4 h-4 mr-2\" />\n        Templates\n      </Link>\n      <Link href=\"/custom-request\" className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center\">\n        <Palette className=\"w-4 h-4 mr-2\" />\n        Customize\n      </Link>\n      <Link href=\"/contact\" className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center\">\n        <Phone className=\"w-4 h-4 mr-2\" />\n        Contact\n      </Link>\n    </>\n  );\n\n  return (\n    <nav className=\"border-b bg-white shadow-sm sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-14 sm:h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <span className=\"font-bold text-lg sm:text-xl text-gray-900\">KaleidoneX</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-6 xl:space-x-8\">\n            <NavLinks />\n          </div>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {user ? (\n              <>\n                <DropdownMenu>\n                  <DropdownMenuTrigger asChild>\n                    <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarFallback>\n                          {userData?.displayName?.[0] || user.email?.[0]?.toUpperCase()}\n                        </AvatarFallback>\n                      </Avatar>\n                    </Button>\n                  </DropdownMenuTrigger>\n                  <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                    <DropdownMenuItem asChild>\n                      <Link href={userData?.role === 'admin' ? '/admin' : '/dashboard'} className=\"flex items-center\">\n                        <User className=\"mr-2 h-4 w-4\" />\n                        {userData?.role === 'admin' ? 'Admin Dashboard' : 'Dashboard'}\n                      </Link>\n                    </DropdownMenuItem>\n                    <DropdownMenuItem asChild>\n                      <Link href=\"/profile\" className=\"flex items-center\">\n                        <Settings className=\"mr-2 h-4 w-4\" />\n                        Profile Settings\n                      </Link>\n                    </DropdownMenuItem>\n                    <DropdownMenuItem asChild>\n                      <Link href=\"/orders\" className=\"flex items-center\">\n                        <ShoppingBag className=\"mr-2 h-4 w-4\" />\n                        My Orders\n                      </Link>\n                    </DropdownMenuItem>\n                    <DropdownMenuSeparator />\n                    <DropdownMenuItem onClick={handleLogout}>\n                      <LogOut className=\"mr-2 h-4 w-4\" />\n                      Log out\n                    </DropdownMenuItem>\n                  </DropdownMenuContent>\n                </DropdownMenu>\n              </>\n            ) : (\n              <div className=\"hidden lg:flex items-center space-x-2\">\n                <Button asChild variant=\"ghost\" className=\"text-gray-700 hover:text-gray-900 text-sm\">\n                  <Link href=\"/auth\" className=\"flex items-center\">\n                    <User className=\"w-4 h-4 mr-2\" />\n                    Sign In\n                  </Link>\n                </Button>\n              </div>\n            )}\n\n            {/* Mobile Menu */}\n            <Sheet open={isOpen} onOpenChange={setIsOpen}>\n              <SheetTrigger asChild className=\"lg:hidden\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"p-2\">\n                  <Menu className=\"h-5 w-5\" />\n                </Button>\n              </SheetTrigger>\n              <SheetContent side=\"right\" className=\"w-[280px] sm:w-[350px]\">\n                <div className=\"flex flex-col space-y-4 mt-6\">\n                  <div className=\"flex flex-col space-y-3\">\n                    <Link\n                      href=\"/\"\n                      className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      <Home className=\"w-4 h-4 mr-3\" />\n                      Home\n                    </Link>\n                    <Link\n                      href=\"/templates\"\n                      className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      <FileText className=\"w-4 h-4 mr-3\" />\n                      Templates\n                    </Link>\n                    <Link\n                      href=\"/custom-request\"\n                      className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      <Palette className=\"w-4 h-4 mr-3\" />\n                      Customize\n                    </Link>\n                    <Link\n                      href=\"/contact\"\n                      className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      <Phone className=\"w-4 h-4 mr-3\" />\n                      Contact\n                    </Link>\n                  </div>\n\n                  {user ? (\n                    <div className=\"flex flex-col space-y-3 pt-4 border-t\">\n                      <button\n                        onClick={() => {\n                          logout();\n                          setIsOpen(false);\n                        }}\n                        className=\"text-left text-sm font-medium text-red-600 hover:text-red-700 transition-colors flex items-center py-2\"\n                      >\n                        <LogOut className=\"w-4 h-4 mr-3\" />\n                        Sign Out\n                      </button>\n                    </div>\n                  ) : (\n                    <div className=\"flex flex-col space-y-3 pt-4 border-t\">\n                      <Link\n                        href=\"/auth\"\n                        className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <User className=\"w-4 h-4 mr-3\" />\n                        Sign In\n                      </Link>\n                      <Link\n                        href=\"/auth?mode=signup\"\n                        className=\"text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors flex items-center py-2\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <Plus className=\"w-4 h-4 mr-3\" />\n                        Get Started\n                      </Link>\n                    </div>\n                  )}\n                </div>\n              </SheetContent>\n            </Sheet>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAfA;;;;;;;;;;AAiBO,MAAM,SAAS;IACpB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,WAAW,kBACf;;8BACE,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,8OAAC,mMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGnC,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAa,WAAU;;sCAChC,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGvC,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAkB,WAAU;;sCACrC,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGtC,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAW,WAAU;;sCAC9B,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;IAMxC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;;;;;;kCAI/D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;;;;;;;;;kCAIH,8OAAC;wBAAI,WAAU;;4BACZ,qBACC;0CACE,cAAA,8OAAC,4IAAA,CAAA,eAAY;;sDACX,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;kEACZ,UAAU,aAAa,CAAC,EAAE,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;sDAKxD,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,WAAU;4CAAO,OAAM;4CAAM,UAAU;;8DAC1D,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,UAAU,SAAS,UAAU,WAAW;wDAAc,WAAU;;0EAC1E,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,UAAU,SAAS,UAAU,oBAAoB;;;;;;;;;;;;8DAGtD,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;0EAC9B,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAIzC,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;;0EAC7B,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAI5C,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;;sEACzB,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8DAO3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAQ,WAAU;8CACxC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;;0DAC3B,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;0CAQzC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,MAAM;gCAAQ,cAAc;;kDACjC,8OAAC,iIAAA,CAAA,eAAY;wCAAC,OAAO;wCAAC,WAAU;kDAC9B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGpB,8OAAC,iIAAA,CAAA,eAAY;wCAAC,MAAK;wCAAQ,WAAU;kDACnC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,UAAU;;8EAEzB,8OAAC,mMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,UAAU;;8EAEzB,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,UAAU;;8EAEzB,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGtC,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,UAAU;;8EAEzB,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;gDAKrC,qBACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS;4DACP;4DACA,UAAU;wDACZ;wDACA,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;yEAKvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,UAAU;;8EAEzB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,UAAU;;8EAEzB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAazD", "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { \n  Facebook, \n  Twitter, \n  Instagram, \n  Linkedin, \n  Github, \n  Mail, \n  Phone, \n  MapPin,\n  Heart\n} from 'lucide-react';\n\nexport const Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto px-4 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-xl font-bold\">KaleidoneX</h3>\n            <p className=\"text-gray-400 text-sm\">\n              Premium templates and digital solutions for modern businesses. \n              Create stunning websites with our professionally designed templates.\n            </p>\n            <div className=\"flex space-x-4\">\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <Facebook className=\"h-5 w-5\" />\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <Twitter className=\"h-5 w-5\" />\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <Instagram className=\"h-5 w-5\" />\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <Linkedin className=\"h-5 w-5\" />\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <Github className=\"h-5 w-5\" />\n              </Link>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h4 className=\"text-lg font-semibold\">Quick Links</h4>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/templates\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  Templates\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/categories\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  Categories\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div className=\"space-y-4\">\n            <h4 className=\"text-lg font-semibold\">Support</h4>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  Help Center\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/documentation\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  Documentation\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  FAQ\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  Privacy Policy\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  Terms of Service\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Newsletter */}\n          <div className=\"space-y-4\">\n            <h4 className=\"text-lg font-semibold\">Stay Updated</h4>\n            <p className=\"text-gray-400 text-sm\">\n              Subscribe to our newsletter for the latest templates and updates.\n            </p>\n            <div className=\"space-y-2\">\n              <Input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"bg-gray-800 border-gray-700 text-white placeholder-gray-400\"\n              />\n              <Button className=\"w-full bg-blue-600 hover:bg-blue-700\">\n                Subscribe\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Contact Info */}\n        <div className=\"border-t border-gray-800 pt-8 mb-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n            <div className=\"flex items-center space-x-2 text-gray-400\">\n              <Mail className=\"h-4 w-4\" />\n              <span><EMAIL></span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-gray-400\">\n              <Phone className=\"h-4 w-4\" />\n              <span>+****************</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-gray-400\">\n              <MapPin className=\"h-4 w-4\" />\n              <span>San Francisco, CA</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <div className=\"border-t border-gray-800 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-sm text-gray-400\">\n              © 2024 KaleidoneX. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-1 text-sm text-gray-400\">\n              <span>Made with</span>\n              <Heart className=\"h-4 w-4 text-red-500 fill-current\" />\n              <span>by KaleidoneX Team</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAkBO,MAAM,SAAS;IACpB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoB;;;;;;8CAClC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAIrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAmD;;;;;;;;;;;sDAI9E,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAmD;;;;;;;;;;;sDAIvF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAmD;;;;;;;;;;;sDAIxF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAAmD;;;;;;;;;;;sDAI3F,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;sDAIjF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQvF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDAAuC;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}]}