{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/add-template/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { ArrowLeft, Plus, Upload } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function AddTemplate() {\n  const { user, userData } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: '',\n    price: '',\n    minPrice: '',\n    maxPrice: '',\n    features: '',\n    demoUrl: '',\n    downloadUrl: '',\n    imageUrl: '',\n    tags: ''\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    \n    try {\n      // Here you would typically save to Firebase/Firestore\n      console.log('Template data:', formData);\n      alert('Template added successfully!');\n      \n      // Reset form\n      setFormData({\n        title: '',\n        description: '',\n        category: '',\n        price: '',\n        minPrice: '',\n        maxPrice: '',\n        features: '',\n        demoUrl: '',\n        downloadUrl: '',\n        imageUrl: '',\n        tags: ''\n      });\n    } catch (error) {\n      console.error('Error adding template:', error);\n      alert('Failed to add template');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  if (!user || userData?.role !== 'admin') {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Access Denied</h1>\n        <p className=\"text-gray-600 mb-4\">You need admin privileges to access this page.</p>\n        <Button asChild>\n          <Link href=\"/dashboard\">Go to Dashboard</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <Button asChild variant=\"outline\" size=\"sm\">\n              <Link href=\"/admin\">\n                <ArrowLeft className=\"mr-2 h-4 w-4\" />\n                Back to Dashboard\n              </Link>\n            </Button>\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Add New Template\n          </h1>\n          <p className=\"text-gray-600\">\n            Add a new template to your marketplace\n          </p>\n        </div>\n\n        <div className=\"max-w-6xl mx-auto\">\n          <Card className=\"shadow-lg border-0\">\n            <CardHeader className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-b\">\n              <CardTitle className=\"flex items-center text-2xl\">\n                <Plus className=\"mr-3 h-6 w-6 text-blue-600\" />\n                Template Details\n              </CardTitle>\n              <CardDescription className=\"text-base\">\n                Fill in the information for your new template. All fields marked with * are required.\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"p-8\">\n              <form onSubmit={handleSubmit} className=\"space-y-8\">\n                {/* Basic Information Section */}\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 border-b pb-2\">Basic Information</h3>\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"title\" className=\"text-sm font-medium\">Template Title *</Label>\n                      <Input\n                        id=\"title\"\n                        value={formData.title}\n                        onChange={(e) => handleInputChange('title', e.target.value)}\n                        placeholder=\"e.g., SaaS Dashboard Pro\"\n                        className=\"h-11\"\n                        required\n                      />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"category\" className=\"text-sm font-medium\">Category *</Label>\n                      <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>\n                        <SelectTrigger className=\"h-11\">\n                          <SelectValue placeholder=\"Select category\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          <SelectItem value=\"Dashboard\">Dashboard</SelectItem>\n                          <SelectItem value=\"E-commerce\">E-commerce</SelectItem>\n                          <SelectItem value=\"Landing Page\">Landing Page</SelectItem>\n                          <SelectItem value=\"Portfolio\">Portfolio</SelectItem>\n                          <SelectItem value=\"Education\">Education</SelectItem>\n                          <SelectItem value=\"Blog\">Blog</SelectItem>\n                          <SelectItem value=\"Business\">Business</SelectItem>\n                          <SelectItem value=\"Technology\">Technology</SelectItem>\n                        </SelectContent>\n                      </Select>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Description Section */}\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 border-b pb-2\">Description & Details</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"description\" className=\"text-sm font-medium\">Description *</Label>\n                    <Textarea\n                      id=\"description\"\n                      value={formData.description}\n                      onChange={(e) => handleInputChange('description', e.target.value)}\n                      placeholder=\"Provide a detailed description of your template, its features, and what makes it unique...\"\n                      rows={4}\n                      className=\"resize-none\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                {/* Media & Links Section */}\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 border-b pb-2\">Media & Links</h3>\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"imageUrl\" className=\"text-sm font-medium\">Image URL *</Label>\n                      <Input\n                        id=\"imageUrl\"\n                        type=\"url\"\n                        value={formData.imageUrl}\n                        onChange={(e) => handleInputChange('imageUrl', e.target.value)}\n                        placeholder=\"https://images.unsplash.com/photo-...\"\n                        className=\"h-11\"\n                        required\n                      />\n                      <p className=\"text-xs text-gray-500\">Use high-quality images from Unsplash or similar services</p>\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"demoUrl\" className=\"text-sm font-medium\">Demo URL</Label>\n                      <Input\n                        id=\"demoUrl\"\n                        type=\"url\"\n                        value={formData.demoUrl}\n                        onChange={(e) => handleInputChange('demoUrl', e.target.value)}\n                        placeholder=\"https://demo.example.com\"\n                        className=\"h-11\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Pricing & Features Section */}\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 border-b pb-2\">Pricing & Features</h3>\n                  <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"price\" className=\"text-sm font-medium\">Fixed Price (₹) *</Label>\n                      <Input\n                        id=\"price\"\n                        type=\"number\"\n                        value={formData.price}\n                        onChange={(e) => handleInputChange('price', e.target.value)}\n                        placeholder=\"2499\"\n                        min=\"0\"\n                        className=\"h-11\"\n                        required\n                      />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"minPrice\" className=\"text-sm font-medium\">Min Price (₹)</Label>\n                      <Input\n                        id=\"minPrice\"\n                        type=\"number\"\n                        value={formData.minPrice}\n                        onChange={(e) => handleInputChange('minPrice', e.target.value)}\n                        placeholder=\"1999\"\n                        min=\"0\"\n                        className=\"h-11\"\n                      />\n                      <p className=\"text-xs text-gray-500\">Optional: For price ranges</p>\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"maxPrice\" className=\"text-sm font-medium\">Max Price (₹)</Label>\n                      <Input\n                        id=\"maxPrice\"\n                        type=\"number\"\n                        value={formData.maxPrice}\n                        onChange={(e) => handleInputChange('maxPrice', e.target.value)}\n                        placeholder=\"4999\"\n                        min=\"0\"\n                        className=\"h-11\"\n                      />\n                      <p className=\"text-xs text-gray-500\">Optional: For price ranges</p>\n                    </div>\n                  </div>\n                  <div className=\"grid grid-cols-1 gap-6\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"tags\" className=\"text-sm font-medium\">Tags</Label>\n                      <Input\n                        id=\"tags\"\n                        value={formData.tags}\n                        onChange={(e) => handleInputChange('tags', e.target.value)}\n                        placeholder=\"React, TypeScript, Tailwind CSS\"\n                        className=\"h-11\"\n                      />\n                      <p className=\"text-xs text-gray-500\">Separate tags with commas</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Features & Download Section */}\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 border-b pb-2\">Features & Download</h3>\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"features\" className=\"text-sm font-medium\">Features (one per line)</Label>\n                      <Textarea\n                        id=\"features\"\n                        value={formData.features}\n                        onChange={(e) => handleInputChange('features', e.target.value)}\n                        placeholder=\"Responsive Design&#10;Dark Mode Support&#10;Admin Dashboard&#10;User Authentication&#10;Modern UI Components\"\n                        rows={6}\n                        className=\"resize-none\"\n                      />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"downloadUrl\" className=\"text-sm font-medium\">Download URL</Label>\n                      <Input\n                        id=\"downloadUrl\"\n                        type=\"url\"\n                        value={formData.downloadUrl}\n                        onChange={(e) => handleInputChange('downloadUrl', e.target.value)}\n                        placeholder=\"https://files.example.com/template.zip\"\n                        className=\"h-11\"\n                      />\n                      <p className=\"text-xs text-gray-500\">Direct link to downloadable template files</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex items-center justify-between pt-8 border-t\">\n                  <Button type=\"button\" variant=\"outline\" size=\"lg\" asChild>\n                    <Link href=\"/admin\">\n                      Cancel\n                    </Link>\n                  </Button>\n                  <Button type=\"submit\" disabled={loading} size=\"lg\" className=\"px-8\">\n                    {loading ? (\n                      <>\n                        <Upload className=\"mr-2 h-4 w-4 animate-spin\" />\n                        Adding Template...\n                      </>\n                    ) : (\n                      <>\n                        <Plus className=\"mr-2 h-4 w-4\" />\n                        Add Template\n                      </>\n                    )}\n                  </Button>\n                </div>\n              </form>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,aAAa;QACb,UAAU;QACV,MAAM;IACR;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,sDAAsD;YACtD,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,MAAM;YAEN,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,IAAI,CAAC,QAAQ,UAAU,SAAS,SAAS;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAa;;;;;;;;;;;;;;;;;IAIhC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,MAAK;0CACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAK/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGjD,8OAAC,gIAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAY;;;;;;;;;;;;0CAIzC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDAEtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;8DAClE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAQ,WAAU;8EAAsB;;;;;;8EACvD,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,OAAO,SAAS,KAAK;oEACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oEAC1D,aAAY;oEACZ,WAAU;oEACV,QAAQ;;;;;;;;;;;;sEAGZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAW,WAAU;8EAAsB;;;;;;8EAC1D,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO,SAAS,QAAQ;oEAAE,eAAe,CAAC,QAAU,kBAAkB,YAAY;;sFACxF,8OAAC,kIAAA,CAAA,gBAAa;4EAAC,WAAU;sFACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;sFAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8FACZ,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAY;;;;;;8FAC9B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAa;;;;;;8FAC/B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAe;;;;;;8FACjC,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAY;;;;;;8FAC9B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAY;;;;;;8FAC9B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAO;;;;;;8FACzB,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAW;;;;;;8FAC7B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;8DAClE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAc,WAAU;sEAAsB;;;;;;sEAC7D,8OAAC,oIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO,SAAS,WAAW;4DAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4DAChE,aAAY;4DACZ,MAAM;4DACN,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAMd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;8DAClE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAW,WAAU;8EAAsB;;;;;;8EAC1D,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,QAAQ;oEACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oEAC7D,aAAY;oEACZ,WAAU;oEACV,QAAQ;;;;;;8EAEV,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAAsB;;;;;;8EACzD,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,OAAO;oEACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oEAC5D,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAOlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;8DAClE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAQ,WAAU;8EAAsB;;;;;;8EACvD,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oEAC1D,aAAY;oEACZ,KAAI;oEACJ,WAAU;oEACV,QAAQ;;;;;;;;;;;;sEAGZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAW,WAAU;8EAAsB;;;;;;8EAC1D,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,QAAQ;oEACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oEAC7D,aAAY;oEACZ,KAAI;oEACJ,WAAU;;;;;;8EAEZ,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAW,WAAU;8EAAsB;;;;;;8EAC1D,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,QAAQ;oEACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oEAC7D,aAAY;oEACZ,KAAI;oEACJ,WAAU;;;;;;8EAEZ,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAO,WAAU;0EAAsB;;;;;;0EACtD,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACzD,aAAY;gEACZ,WAAU;;;;;;0EAEZ,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;sDAM3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;8DAClE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAW,WAAU;8EAAsB;;;;;;8EAC1D,8OAAC,oIAAA,CAAA,WAAQ;oEACP,IAAG;oEACH,OAAO,SAAS,QAAQ;oEACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oEAC7D,aAAY;oEACZ,MAAM;oEACN,WAAU;;;;;;;;;;;;sEAGd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAc,WAAU;8EAAsB;;;;;;8EAC7D,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,WAAW;oEAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oEAChE,aAAY;oEACZ,WAAU;;;;;;8EAEZ,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAM3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAS,SAAQ;oDAAU,MAAK;oDAAK,OAAO;8DACvD,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAS;;;;;;;;;;;8DAItB,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAS,UAAU;oDAAS,MAAK;oDAAK,WAAU;8DAC1D,wBACC;;0EACE,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAA8B;;qFAIlD;;0EACE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAazD", "debugId": null}}]}