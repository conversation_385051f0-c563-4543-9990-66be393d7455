{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  query,\n  orderBy,\n  onSnapshot\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...requestData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n    \n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n    \n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'contactMessages'), {\n      ...messageData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating contact message:', error);\n    throw error;\n  }\n};\n\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    await updateDoc(doc(db, 'contactMessages', messageId), updateData);\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n\nexport const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {\n  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const messages = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n    callback(messages);\n  });\n};\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAWA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB;YAC7D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB,QAAkC;IACpG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,CAAC;IACzC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAExE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/StatusDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  ChevronDown,\n  Clock,\n  PlayCircle,\n  CheckCircle,\n  XCircle,\n  MessageSquare,\n  Loader2\n} from 'lucide-react';\n\ninterface StatusDropdownProps {\n  currentStatus: string;\n  onStatusChange: (status: string) => Promise<void>;\n  type: 'custom-request' | 'contact-message' | 'purchase-request';\n  disabled?: boolean;\n}\n\nconst getStatusConfig = (status: string, type: 'custom-request' | 'contact-message' | 'purchase-request') => {\n  if (type === 'custom-request') {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'in-progress':\n        return { icon: PlayCircle, label: 'In Progress', variant: 'default' as const, color: 'text-blue-600' };\n      case 'completed':\n        return { icon: CheckCircle, label: 'Completed', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'cancelled':\n        return { icon: XCircle, label: 'Cancelled', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  } else if (type === 'purchase-request') {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'confirmed':\n        return { icon: CheckCircle, label: 'Confirmed', variant: 'secondary' as const, color: 'text-blue-600' };\n      case 'approved':\n        return { icon: CheckCircle, label: 'Order Approved', variant: 'default' as const, color: 'text-green-600' };\n      case 'completed':\n        return { icon: CheckCircle, label: 'Completed', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'declined':\n        return { icon: XCircle, label: 'Declined', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  } else {\n    switch (status) {\n      case 'pending':\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n      case 'responded':\n        return { icon: MessageSquare, label: 'Responded', variant: 'secondary' as const, color: 'text-blue-600' };\n      case 'in-progress':\n        return { icon: PlayCircle, label: 'In Progress', variant: 'default' as const, color: 'text-blue-600' };\n      case 'resolved':\n        return { icon: CheckCircle, label: 'Resolved', variant: 'secondary' as const, color: 'text-green-600' };\n      case 'declined':\n        return { icon: XCircle, label: 'Declined', variant: 'destructive' as const, color: 'text-red-600' };\n      default:\n        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };\n    }\n  }\n};\n\nconst getAvailableStatuses = (currentStatus: string, type: 'custom-request' | 'contact-message' | 'purchase-request') => {\n  if (type === 'custom-request') {\n    switch (currentStatus) {\n      case 'pending':\n        return [\n          { value: 'in-progress', label: 'Accept & Start', icon: PlayCircle },\n          { value: 'cancelled', label: 'Decline', icon: XCircle }\n        ];\n      case 'in-progress':\n        return [\n          { value: 'completed', label: 'Mark Complete', icon: CheckCircle }\n        ];\n      default:\n        return [];\n    }\n  } else if (type === 'purchase-request') {\n    switch (currentStatus) {\n      case 'pending':\n        return [\n          { value: 'confirmed', label: 'Confirm Order', icon: CheckCircle },\n          { value: 'declined', label: 'Decline', icon: XCircle }\n        ];\n      case 'confirmed':\n        return [\n          { value: 'approved', label: 'Approve Order', icon: CheckCircle },\n          { value: 'declined', label: 'Decline', icon: XCircle }\n        ];\n      case 'approved':\n        return [\n          { value: 'completed', label: 'Mark Complete', icon: CheckCircle }\n        ];\n      default:\n        return [];\n    }\n  } else {\n    const allStatuses = [\n      { value: 'responded', label: 'Respond', icon: MessageSquare },\n      { value: 'in-progress', label: 'In Progress', icon: PlayCircle },\n      { value: 'declined', label: 'Decline', icon: XCircle },\n      { value: 'resolved', label: 'Resolve', icon: CheckCircle }\n    ];\n\n    // Filter out current status and resolved/declined if already set\n    return allStatuses.filter(status => {\n      if (status.value === currentStatus) return false;\n      if ((currentStatus === 'resolved' || currentStatus === 'declined') &&\n          (status.value === 'resolved' || status.value === 'declined')) return false;\n      return true;\n    });\n  }\n};\n\nexport default function StatusDropdown({ currentStatus, onStatusChange, type, disabled = false }: StatusDropdownProps) {\n  const [isUpdating, setIsUpdating] = useState(false);\n  const statusConfig = getStatusConfig(currentStatus, type);\n  const availableStatuses = getAvailableStatuses(currentStatus, type);\n  const StatusIcon = statusConfig.icon;\n\n  const handleStatusChange = async (newStatus: string) => {\n    setIsUpdating(true);\n    try {\n      await onStatusChange(newStatus);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  if (availableStatuses.length === 0) {\n    return (\n      <Badge variant={statusConfig.variant} className=\"flex items-center gap-1\">\n        <StatusIcon className=\"h-3 w-3\" />\n        {statusConfig.label}\n      </Badge>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <Badge variant={statusConfig.variant} className=\"flex items-center gap-1\">\n        <StatusIcon className=\"h-3 w-3\" />\n        {statusConfig.label}\n      </Badge>\n      \n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            disabled={disabled || isUpdating}\n            className=\"h-8 px-2\"\n          >\n            {isUpdating ? (\n              <Loader2 className=\"h-3 w-3 animate-spin\" />\n            ) : (\n              <>\n                <span className=\"sr-only sm:not-sr-only\">Update</span>\n                <ChevronDown className=\"h-3 w-3 ml-1\" />\n              </>\n            )}\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent align=\"end\" className=\"w-40\">\n          {availableStatuses.map((status) => {\n            const Icon = status.icon;\n            return (\n              <DropdownMenuItem\n                key={status.value}\n                onClick={() => handleStatusChange(status.value)}\n                className=\"flex items-center gap-2 cursor-pointer\"\n              >\n                <Icon className=\"h-4 w-4\" />\n                {status.label}\n              </DropdownMenuItem>\n            );\n          })}\n        </DropdownMenuContent>\n      </DropdownMenu>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;AA4BA,MAAM,kBAAkB,CAAC,QAAgB;IACvC,IAAI,SAAS,kBAAkB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,kNAAA,CAAA,aAAU;oBAAE,OAAO;oBAAe,SAAS;oBAAoB,OAAO;gBAAgB;YACvG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAiB;YACzG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAa,SAAS;oBAAwB,OAAO;gBAAe;YACrG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF,OAAO,IAAI,SAAS,oBAAoB;QACtC,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAgB;YACxG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAkB,SAAS;oBAAoB,OAAO;gBAAiB;YAC5G,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAiB;YACzG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAY,SAAS;oBAAwB,OAAO;gBAAe;YACpG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF,OAAO;QACL,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;YAChG,KAAK;gBACH,OAAO;oBAAE,MAAM,wNAAA,CAAA,gBAAa;oBAAE,OAAO;oBAAa,SAAS;oBAAsB,OAAO;gBAAgB;YAC1G,KAAK;gBACH,OAAO;oBAAE,MAAM,kNAAA,CAAA,aAAU;oBAAE,OAAO;oBAAe,SAAS;oBAAoB,OAAO;gBAAgB;YACvG,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAY,SAAS;oBAAsB,OAAO;gBAAiB;YACxG,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAY,SAAS;oBAAwB,OAAO;gBAAe;YACpG;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,OAAO;gBAAkB;QAClG;IACF;AACF;AAEA,MAAM,uBAAuB,CAAC,eAAuB;IACnD,IAAI,SAAS,kBAAkB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAe,OAAO;wBAAkB,MAAM,kNAAA,CAAA,aAAU;oBAAC;oBAClE;wBAAE,OAAO;wBAAa,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACvD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;iBACjE;YACH;gBACE,OAAO,EAAE;QACb;IACF,OAAO,IAAI,SAAS,oBAAoB;QACtC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;oBAChE;wBAAE,OAAO;wBAAY,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACtD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAY,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;oBAC/D;wBAAE,OAAO;wBAAY,OAAO;wBAAW,MAAM,4MAAA,CAAA,UAAO;oBAAC;iBACtD;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAa,OAAO;wBAAiB,MAAM,2NAAA,CAAA,cAAW;oBAAC;iBACjE;YACH;gBACE,OAAO,EAAE;QACb;IACF,OAAO;QACL,MAAM,cAAc;YAClB;gBAAE,OAAO;gBAAa,OAAO;gBAAW,MAAM,wNAAA,CAAA,gBAAa;YAAC;YAC5D;gBAAE,OAAO;gBAAe,OAAO;gBAAe,MAAM,kNAAA,CAAA,aAAU;YAAC;YAC/D;gBAAE,OAAO;gBAAY,OAAO;gBAAW,MAAM,4MAAA,CAAA,UAAO;YAAC;YACrD;gBAAE,OAAO;gBAAY,OAAO;gBAAW,MAAM,2NAAA,CAAA,cAAW;YAAC;SAC1D;QAED,iEAAiE;QACjE,OAAO,YAAY,MAAM,CAAC,CAAA;YACxB,IAAI,OAAO,KAAK,KAAK,eAAe,OAAO;YAC3C,IAAI,CAAC,kBAAkB,cAAc,kBAAkB,UAAU,KAC7D,CAAC,OAAO,KAAK,KAAK,cAAc,OAAO,KAAK,KAAK,UAAU,GAAG,OAAO;YACzE,OAAO;QACT;IACF;AACF;AAEe,SAAS,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,IAAI,EAAE,WAAW,KAAK,EAAuB;IACnH,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,gBAAgB,eAAe;IACpD,MAAM,oBAAoB,qBAAqB,eAAe;IAC9D,MAAM,aAAa,aAAa,IAAI;IAEpC,MAAM,qBAAqB,OAAO;QAChC,cAAc;QACd,IAAI;YACF,MAAM,eAAe;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAClC,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAS,aAAa,OAAO;YAAE,WAAU;;8BAC9C,8OAAC;oBAAW,WAAU;;;;;;gBACrB,aAAa,KAAK;;;;;;;IAGzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,aAAa,OAAO;gBAAE,WAAU;;kCAC9C,8OAAC;wBAAW,WAAU;;;;;;oBACrB,aAAa,KAAK;;;;;;;0BAGrB,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,UAAU,YAAY;4BACtB,WAAU;sCAET,2BACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB;;kDACE,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;kDACzC,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAK/B,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAM,WAAU;kCACxC,kBAAkB,GAAG,CAAC,CAAC;4BACtB,MAAM,OAAO,OAAO,IAAI;4BACxB,qBACE,8OAAC,4IAAA,CAAA,mBAAgB;gCAEf,SAAS,IAAM,mBAAmB,OAAO,KAAK;gCAC9C,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;;;;;;oCACf,OAAO,KAAK;;+BALR,OAAO,KAAK;;;;;wBAQvB;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/custom-requests/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport {\n  FileText,\n  Clock,\n  DollarSign,\n  User,\n  Calendar,\n  MessageSquare,\n  CheckCircle,\n  XCircle,\n  PlayCircle,\n  Loader2,\n  AlertCircle,\n  Filter\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { getCustomRequests, updateCustomRequestStatus } from '@/lib/firebaseServices';\nimport { CustomRequest } from '@/types';\nimport StatusDropdown from '@/components/admin/StatusDropdown';\n\nexport default function AdminCustomRequestsPage() {\n  const { user, userData } = useAuth();\n  const [requests, setRequests] = useState<CustomRequest[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedRequest, setSelectedRequest] = useState<CustomRequest | null>(null);\n  const [adminNotes, setAdminNotes] = useState('');\n  const [updating, setUpdating] = useState(false);\n  const [statusFilter, setStatusFilter] = useState('all');\n\n  useEffect(() => {\n    const fetchRequests = async () => {\n      try {\n        setLoading(true);\n        const fetchedRequests = await getCustomRequests();\n        setRequests(fetchedRequests);\n      } catch (error: any) {\n        console.error('Error fetching custom requests:', error);\n        setError('Failed to load custom requests');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && userData?.role === 'admin') {\n      fetchRequests();\n    }\n  }, [user, userData]);\n\n  const handleUpdateStatus = async (requestId: string, status: CustomRequest['status']) => {\n    try {\n      setUpdating(true);\n      await updateCustomRequestStatus(requestId, status, adminNotes);\n      \n      // Update local state\n      setRequests(prev => prev.map(req => \n        req.id === requestId \n          ? { ...req, status, adminNotes, updatedAt: new Date() }\n          : req\n      ));\n      \n      setSelectedRequest(null);\n      setAdminNotes('');\n    } catch (error: any) {\n      console.error('Error updating request status:', error);\n      setError('Failed to update request status');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  if (!user || userData?.role !== 'admin') {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Access Denied</h1>\n        <p className=\"text-gray-600 mb-4\">You need admin privileges to access this page.</p>\n        <Button asChild>\n          <Link href=\"/dashboard\">Go to Dashboard</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  const getStatusIcon = (status: CustomRequest['status']) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"h-4 w-4\" />;\n      case 'in-progress':\n        return <PlayCircle className=\"h-4 w-4\" />;\n      case 'completed':\n        return <CheckCircle className=\"h-4 w-4\" />;\n      case 'cancelled':\n        return <XCircle className=\"h-4 w-4\" />;\n      default:\n        return <Clock className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getStatusColor = (status: CustomRequest['status']) => {\n    switch (status) {\n      case 'pending':\n        return 'outline';\n      case 'in-progress':\n        return 'secondary';\n      case 'completed':\n        return 'default';\n      case 'cancelled':\n        return 'destructive';\n      default:\n        return 'outline';\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Custom Requests Management\n        </h1>\n        <p className=\"text-gray-600\">\n          Manage and respond to custom design requests from users\n        </p>\n      </div>\n\n      {/* Loading State */}\n      {loading && (\n        <div className=\"flex justify-center items-center py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading custom requests...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <Alert variant=\"destructive\" className=\"mb-6\">\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      {/* Stats */}\n      {!loading && (\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"h-5 w-5 text-yellow-600\" />\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n                  <p className=\"text-xl font-bold\">\n                    {requests.filter(r => r.status === 'pending').length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <PlayCircle className=\"h-5 w-5 text-blue-600\" />\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">In Progress</p>\n                  <p className=\"text-xl font-bold\">\n                    {requests.filter(r => r.status === 'in-progress').length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Completed</p>\n                  <p className=\"text-xl font-bold\">\n                    {requests.filter(r => r.status === 'completed').length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <FileText className=\"h-5 w-5 text-gray-600\" />\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total</p>\n                  <p className=\"text-xl font-bold\">{requests.length}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Requests List */}\n      {!loading && (\n        <div className=\"space-y-4\">\n          {requests.length > 0 ? (\n            requests.map((request) => (\n              <Card key={request.id}>\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-3 mb-3\">\n                        <h3 className=\"text-lg font-semibold text-gray-900\">\n                          {request.title}\n                        </h3>\n                        <Badge variant={getStatusColor(request.status)} className=\"flex items-center space-x-1\">\n                          {getStatusIcon(request.status)}\n                          <span>{request.status}</span>\n                        </Badge>\n                      </div>\n                      \n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                        <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                          <User className=\"h-4 w-4\" />\n                          <span>{request.userEmail}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                          <FileText className=\"h-4 w-4\" />\n                          <span>{request.category}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                          <Calendar className=\"h-4 w-4\" />\n                          <span>{new Date(request.createdAt).toLocaleDateString()}</span>\n                        </div>\n                      </div>\n                      \n                      {request.budget && (\n                        <div className=\"flex items-center space-x-2 text-sm text-gray-600 mb-3\">\n                          <DollarSign className=\"h-4 w-4\" />\n                          <span>Budget: ${request.budget}</span>\n                        </div>\n                      )}\n                      \n                      <p className=\"text-gray-700 mb-4\">{request.description}</p>\n                      \n                      {request.adminNotes && (\n                        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <MessageSquare className=\"h-4 w-4 text-blue-600\" />\n                            <span className=\"text-sm font-medium text-blue-800\">Admin Notes</span>\n                          </div>\n                          <p className=\"text-sm text-blue-700\">{request.adminNotes}</p>\n                        </div>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2 ml-4\">\n                      <StatusDropdown\n                        currentStatus={request.status}\n                        onStatusChange={(status) => handleUpdateStatus(request.id, status)}\n                        type=\"custom-request\"\n                      />\n                      {(request.status === 'pending' || request.status === 'in-progress') && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => setSelectedRequest(request)}\n                        >\n                          Add Notes\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))\n          ) : (\n            <Card>\n              <CardContent className=\"p-12 text-center\">\n                <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Custom Requests</h3>\n                <p className=\"text-gray-600\">No custom design requests have been submitted yet.</p>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      )}\n\n      {/* Request Management Modal */}\n      {selectedRequest && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <Card className=\"w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n            <CardHeader>\n              <CardTitle>Manage Request: {selectedRequest.title}</CardTitle>\n              <CardDescription>\n                Update the status and add admin notes for this request\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <Label htmlFor=\"adminNotes\">Admin Notes</Label>\n                <Textarea\n                  id=\"adminNotes\"\n                  placeholder=\"Add notes about this request...\"\n                  value={adminNotes}\n                  onChange={(e) => setAdminNotes(e.target.value)}\n                  rows={4}\n                />\n              </div>\n              \n              <div className=\"flex space-x-2\">\n                {selectedRequest.status === 'pending' && (\n                  <>\n                    <Button \n                      onClick={() => handleUpdateStatus(selectedRequest.id, 'in-progress')}\n                      disabled={updating}\n                    >\n                      {updating && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                      Accept & Start\n                    </Button>\n                    <Button \n                      variant=\"destructive\"\n                      onClick={() => handleUpdateStatus(selectedRequest.id, 'cancelled')}\n                      disabled={updating}\n                    >\n                      {updating && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                      Decline\n                    </Button>\n                  </>\n                )}\n                \n                {selectedRequest.status === 'in-progress' && (\n                  <Button \n                    onClick={() => handleUpdateStatus(selectedRequest.id, 'completed')}\n                    disabled={updating}\n                  >\n                    {updating && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                    Mark Complete\n                  </Button>\n                )}\n                \n                <Button \n                  variant=\"outline\" \n                  onClick={() => {\n                    setSelectedRequest(null);\n                    setAdminNotes('');\n                  }}\n                >\n                  Cancel\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAEA;AA5BA;;;;;;;;;;;;;;AA8Be,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,MAAM,kBAAkB,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;gBAC9C,YAAY;YACd,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,QAAQ,UAAU,SAAS,SAAS;YACtC;QACF;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI;YACF,YAAY;YACZ,MAAM,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW,QAAQ;YAEnD,qBAAqB;YACrB,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,YACP;wBAAE,GAAG,GAAG;wBAAE;wBAAQ;wBAAY,WAAW,IAAI;oBAAO,IACpD;YAGN,mBAAmB;YACnB,cAAc;QAChB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,YAAY;QACd;IACF;IAEA,IAAI,CAAC,QAAQ,UAAU,SAAS,SAAS;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAa;;;;;;;;;;;;;;;;;IAIhC;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAM9B,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMlC,uBACC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAc,WAAU;0BACrC,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;8BAAE;;;;;;;;;;;YAKtB,CAAC,yBACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9D,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqB,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS5D,CAAC,yBACA,8OAAC;gBAAI,WAAU;0BACZ,SAAS,MAAM,GAAG,IACjB,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,eAAe,QAAQ,MAAM;wDAAG,WAAU;;4DACvD,cAAc,QAAQ,MAAM;0EAC7B,8OAAC;0EAAM,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0DAIzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAM,QAAQ,SAAS;;;;;;;;;;;;kEAE1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,QAAQ,QAAQ;;;;;;;;;;;;kEAEzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;4CAIxD,QAAQ,MAAM,kBACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;;4DAAK;4DAAU,QAAQ,MAAM;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;4CAErD,QAAQ,UAAU,kBACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EACzB,8OAAC;gEAAK,WAAU;0EAAoC;;;;;;;;;;;;kEAEtD,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;kDAK9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6IAAA,CAAA,UAAc;gDACb,eAAe,QAAQ,MAAM;gDAC7B,gBAAgB,CAAC,SAAW,mBAAmB,QAAQ,EAAE,EAAE;gDAC3D,MAAK;;;;;;4CAEN,CAAC,QAAQ,MAAM,KAAK,aAAa,QAAQ,MAAM,KAAK,aAAa,mBAChE,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,mBAAmB;0DACnC;;;;;;;;;;;;;;;;;;;;;;;uBA5DA,QAAQ,EAAE;;;;8CAsEvB,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;YAQtC,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;;wCAAC;wCAAiB,gBAAgB,KAAK;;;;;;;8CACjD,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,MAAM;;;;;;;;;;;;8CAIV,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,MAAM,KAAK,2BAC1B;;8DACE,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,mBAAmB,gBAAgB,EAAE,EAAE;oDACtD,UAAU;;wDAET,0BAAY,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;8DAGjE,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,mBAAmB,gBAAgB,EAAE,EAAE;oDACtD,UAAU;;wDAET,0BAAY,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;;;wCAMpE,gBAAgB,MAAM,KAAK,+BAC1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,mBAAmB,gBAAgB,EAAE,EAAE;4CACtD,UAAU;;gDAET,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;sDAKnE,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;gDACP,mBAAmB;gDACnB,cAAc;4CAChB;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}