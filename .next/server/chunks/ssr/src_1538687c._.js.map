{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nexport const HeroSection = () => {\n  return (\n    <section className=\"bg-white py-12 sm:py-16 lg:py-24\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Hero Content */}\n          <div className=\"mb-8 sm:mb-12\">\n            <h1 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight\">\n              Premium Templates for\n              <span className=\"text-blue-600\">\n                {\" \"}Your Business\n              </span>\n            </h1>\n            <p className=\"text-base sm:text-lg text-gray-600 mb-6 sm:mb-8 max-w-2xl mx-auto\">\n              Professional, ready-to-use templates that help you build beautiful websites\n              and applications quickly and efficiently.\n            </p>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mb-12 sm:mb-16\">\n            <Button asChild size=\"lg\" className=\"px-6 sm:px-8 py-3\">\n              <Link href=\"/templates\">\n                Browse Templates\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"px-6 sm:px-8 py-3\">\n              <Link href=\"/contact\">\n                Get Started\n              </Link>\n            </Button>\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-3xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">500+</div>\n              <div className=\"text-sm sm:text-base text-gray-600\">Premium Templates</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">10,000+</div>\n              <div className=\"text-sm sm:text-base text-gray-600\">Happy Customers</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">24/7</div>\n              <div className=\"text-sm sm:text-base text-gray-600\">Support</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAsF;kDAElG,8OAAC;wCAAK,WAAU;;4CACb;4CAAI;;;;;;;;;;;;;0CAGT,8OAAC;gCAAE,WAAU;0CAAoE;;;;;;;;;;;;kCAOnF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,MAAK;gCAAK,WAAU;0CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAa;;;;;;;;;;;0CAI1B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;;;;;;kCAO1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoD;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoD;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoD;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  query,\n  orderBy,\n  onSnapshot\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...requestData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n    \n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n    \n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'contactMessages'), {\n      ...messageData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating contact message:', error);\n    throw error;\n  }\n};\n\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    await updateDoc(doc(db, 'contactMessages', messageId), updateData);\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n\nexport const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {\n  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const messages = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n    callback(messages);\n  });\n};\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAWA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB;YAC7D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB,QAAkC;IACpG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,CAAC;IACzC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAExE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/FeaturedTemplates.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Star, Eye, Download, ArrowRight, ShoppingCart, MessageCircle, Heart, ExternalLink, Zap, IndianRupee } from 'lucide-react';\nimport { Template } from '@/types';\nimport { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';\nimport { db } from '@/lib/firebase';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { createContactMessage } from '@/lib/firebaseServices';\nimport { toast } from 'sonner';\nimport { useRouter } from 'next/navigation';\n\n// Fallback data if Firebase fails\nconst fallbackTemplates = [\n  {\n    id: '1',\n    title: 'Modern Dashboard',\n    description: 'Clean and modern dashboard template with dark mode support',\n    category: 'Dashboard',\n    price: 49,\n    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center',\n    rating: 4.9,\n    downloads: 1234,\n    featured: true,\n    tags: ['React', 'TypeScript', 'Tailwind']\n  },\n  {\n    id: '2',\n    title: 'E-commerce Store',\n    description: 'Complete e-commerce solution with shopping cart and checkout',\n    category: 'E-commerce',\n    price: 79,\n    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop&crop=center',\n    rating: 4.8,\n    downloads: 856,\n    featured: true,\n    tags: ['Next.js', 'Stripe', 'Responsive']\n  },\n  {\n    id: '3',\n    title: 'Landing Page Pro',\n    description: 'High-converting landing page template for SaaS products',\n    category: 'Landing Page',\n    price: 39,\n    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop&crop=center',\n    rating: 4.9,\n    downloads: 2341,\n    featured: true,\n    tags: ['HTML', 'CSS', 'JavaScript']\n  },\n  {\n    id: '4',\n    title: 'Portfolio Showcase',\n    description: 'Creative portfolio template for designers and developers',\n    category: 'Portfolio',\n    price: 29,\n    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=center',\n    rating: 4.7,\n    downloads: 1567,\n    featured: true,\n    tags: ['Vue.js', 'GSAP', 'Responsive']\n  }\n];\n\nexport const FeaturedTemplates = () => {\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [loading, setLoading] = useState(true);\n  const { user, userData } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    const fetchFeaturedTemplates = async () => {\n      try {\n        // First try to get featured templates without ordering to avoid index requirement\n        const q = query(\n          collection(db, 'templates'),\n          where('featured', '==', true),\n          limit(8)\n        );\n        const querySnapshot = await getDocs(q);\n        let fetchedTemplates = querySnapshot.docs.map(doc => ({\n          id: doc.id,\n          ...doc.data()\n        })) as Template[];\n\n        // Sort by downloads in memory to avoid index requirement\n        fetchedTemplates = fetchedTemplates\n          .sort((a, b) => (b.downloads || 0) - (a.downloads || 0))\n          .slice(0, 4);\n\n        setTemplates(fetchedTemplates.length > 0 ? fetchedTemplates : fallbackTemplates);\n      } catch (error) {\n        console.error('Error fetching featured templates:', error);\n        setTemplates(fallbackTemplates);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchFeaturedTemplates();\n  }, []);\n\n  const handleContactRequest = async (template: Template) => {\n    if (!user) {\n      toast.error('Please sign in to contact us')\n      router.push('/auth')\n      return\n    }\n\n    // Check if user has completed profile (mobile number required)\n    if (!userData?.phoneNumber || !userData?.fullName) {\n      toast.error('Please complete your profile with mobile number before contacting us')\n      router.push('/profile')\n      return\n    }\n\n    try {\n      await createContactMessage({\n        userId: user.uid,\n        userEmail: user.email!,\n        userName: userData.fullName,\n        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,\n        subject: `Inquiry about ${template.title}`,\n        message: `Hi, I'm interested in the ${template.title} template. Could you please provide more information about customization options and pricing?`,\n        type: 'contact',\n        templateId: template.id,\n        templateTitle: template.title,\n        status: 'pending'\n      })\n\n      toast.success('Contact request sent! We\\'ll get back to you soon.')\n    } catch (error) {\n      console.error('Error sending contact request:', error)\n      toast.error('Failed to send contact request. Please try again.')\n    }\n  }\n\n  const handleBuyRequest = async (template: Template) => {\n    if (!user) {\n      toast.error('Please sign in to make a purchase request')\n      router.push('/auth')\n      return\n    }\n\n    // Check if user has completed profile (mobile number required)\n    if (!userData?.phoneNumber || !userData?.fullName) {\n      toast.error('Please complete your profile with mobile number before making a purchase request')\n      router.push('/profile')\n      return\n    }\n\n    try {\n      await createContactMessage({\n        userId: user.uid,\n        userEmail: user.email!,\n        userName: userData.fullName,\n        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,\n        subject: `Purchase Request for ${template.title}`,\n        message: `Hi, I would like to purchase the ${template.title} template. Please provide payment instructions and delivery details.`,\n        type: 'purchase-request',\n        templateId: template.id,\n        templateTitle: template.title,\n        status: 'pending'\n      })\n\n      toast.success('Purchase request sent! We\\'ll contact you with payment details.')\n    } catch (error) {\n      console.error('Error sending buy request:', error)\n      toast.error('Failed to send purchase request. Please try again.')\n    }\n  }\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Featured Templates\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Hand-picked premium templates for your next project\n          </p>\n        </div>\n\n        {/* Templates Grid */}\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n            {[1, 2, 3, 4].map((i) => (\n              <Card key={i} className=\"animate-pulse\">\n                <div className=\"h-48 bg-gray-200 rounded-t-lg\"></div>\n                <CardContent className=\"p-6\">\n                  <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\n                  <div className=\"h-6 bg-gray-200 rounded mb-2\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded mb-4\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded\"></div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n            {templates.map((template) => (\n              <Card key={template.id} className=\"group overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 shadow-lg\">\n                {/* Image Container with Overlay */}\n                <div className=\"aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden\">\n                  {template.imageUrl ? (\n                    <Image\n                      src={template.imageUrl}\n                      alt={template.title}\n                      width={400}\n                      height={300}\n                      className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                      onError={(e) => {\n                        const target = e.target as HTMLImageElement;\n                        target.style.display = 'none';\n                      }}\n                      unoptimized\n                    />\n                  ) : null}\n                  {!template.imageUrl && (\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\n                      <div className=\"text-center space-y-2\">\n                        <div className=\"w-12 sm:w-16 h-12 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto\">\n                          <Zap className=\"h-6 w-6 sm:h-8 sm:w-8 text-blue-600\" />\n                        </div>\n                        <p className=\"text-muted-foreground font-medium text-sm sm:text-base\">Template Preview</p>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Overlay with Quick Actions */}\n                  <div className=\"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-2 sm:gap-3\">\n                    <Button\n                      size=\"sm\"\n                      variant=\"secondary\"\n                      className=\"bg-white/90 hover:bg-white text-black border-0 text-xs sm:text-sm\"\n                      onClick={() => window.open('/templates', '_blank')}\n                    >\n                      <Eye className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2\" />\n                      Preview\n                    </Button>\n                  </div>\n\n                  {/* Category Badge */}\n                  <div className=\"absolute top-2 sm:top-3 left-2 sm:left-3\">\n                    <Badge variant=\"secondary\" className=\"bg-white/90 text-black border-0 text-xs\">\n                      {template.category}\n                    </Badge>\n                  </div>\n\n                  {/* Favorite Button */}\n                  <div className=\"absolute top-2 sm:top-3 right-2 sm:right-3\">\n                    <Button\n                      size=\"sm\"\n                      variant=\"secondary\"\n                      className=\"w-6 h-6 sm:w-8 sm:h-8 p-0 bg-white/90 hover:bg-white text-black border-0 rounded-full\"\n                    >\n                      <Heart className=\"h-3 w-3 sm:h-4 sm:w-4\" />\n                    </Button>\n                  </div>\n                </div>\n\n                {/* Card Content */}\n                <CardHeader className=\"pb-3\">\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-start justify-between\">\n                      <CardTitle className=\"text-base sm:text-xl font-bold group-hover:text-blue-600 transition-colors line-clamp-2\">\n                        {template.title}\n                      </CardTitle>\n                      <div className=\"flex items-center gap-1 text-yellow-500 flex-shrink-0 ml-2\">\n                        <Star className=\"h-3 w-3 sm:h-4 sm:w-4 fill-current\" />\n                        <span className=\"text-xs sm:text-sm font-medium\">{template.rating || '4.9'}</span>\n                      </div>\n                    </div>\n                    <p className=\"text-xs sm:text-sm leading-relaxed line-clamp-2 text-gray-600\">\n                      {template.description}\n                    </p>\n                  </div>\n                </CardHeader>\n\n                <CardContent className=\"pt-0\">\n                  {/* Features */}\n                  <div className=\"flex flex-wrap gap-1 mb-3 sm:mb-4\">\n                    <Badge variant=\"outline\" className=\"text-xs\">Responsive</Badge>\n                    <Badge variant=\"outline\" className=\"text-xs\">Modern</Badge>\n                    <Badge variant=\"outline\" className=\"text-xs\">Fast</Badge>\n                  </div>\n\n                  {/* Price and Actions */}\n                  <div className=\"space-y-3 sm:space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-1\">\n                        <IndianRupee className=\"h-4 w-4 sm:h-5 sm:w-5 text-green-600\" />\n                        {template.minPrice && template.maxPrice ? (\n                          <span className=\"text-2xl sm:text-3xl font-bold text-green-600\">\n                            ₹{template.minPrice} - ₹{template.maxPrice}\n                          </span>\n                        ) : (\n                          <span className=\"text-2xl sm:text-3xl font-bold text-green-600\">₹{template.price}</span>\n                        )}\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-xs text-muted-foreground line-through\">₹{Math.round(template.price * 1.5)}</div>\n                        <div className=\"text-xs text-green-600 font-medium\">33% OFF</div>\n                      </div>\n                    </div>\n\n                    {/* Action Buttons */}\n                    <div className=\"space-y-2\">\n                      <div className=\"grid grid-cols-2 gap-2\">\n                        <Button\n                          variant=\"outline\"\n                          className=\"w-full group/btn hover:bg-blue-50 hover:border-blue-200 hover:text-blue-700 transition-all text-xs sm:text-sm cursor-pointer\"\n                          onClick={() => window.open('/customize', '_blank')}\n                        >\n                          <Eye className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform\" />\n                          <span className=\"hidden sm:inline\">Preview</span>\n                          <span className=\"sm:hidden\">View</span>\n                        </Button>\n\n                        <Button\n                          className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 font-medium group/btn text-xs sm:text-sm cursor-pointer\"\n                          onClick={() => handleBuyRequest(template)}\n                        >\n                          <MessageCircle className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform\" />\n                          <span className=\"hidden sm:inline\">Contact to Buy</span>\n                          <span className=\"sm:hidden\">Buy</span>\n                        </Button>\n                      </div>\n\n                      <Button\n                        variant=\"outline\"\n                        className=\"w-full group/btn hover:bg-green-50 hover:border-green-200 hover:text-green-700 transition-all text-xs sm:text-sm\"\n                        onClick={() => handleContactRequest(template)}\n                      >\n                        <MessageCircle className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform\" />\n                        <span className=\"hidden sm:inline\">Contact for Info</span>\n                        <span className=\"sm:hidden\">Contact</span>\n                      </Button>\n                    </div>\n\n                    {/* Quick Info */}\n                    <div className=\"flex items-center justify-between text-xs text-muted-foreground pt-2 border-t\">\n                      <span className=\"flex items-center gap-1\">\n                        <Download className=\"h-3 w-3\" />\n                        <span className=\"hidden sm:inline\">{(template.downloads || 1200).toLocaleString()} downloads</span>\n                        <span className=\"sm:hidden\">{(template.downloads || 1200) > 1000 ? `${Math.floor((template.downloads || 1200) / 1000)}k` : template.downloads}</span>\n                      </span>\n                      <span className=\"hidden sm:inline\">Updated recently</span>\n                      <span className=\"sm:hidden\">Recent</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n\n        {/* View All Button */}\n        <div className=\"text-center\">\n          <Button asChild size=\"lg\" variant=\"outline\">\n            <Link href=\"/templates\">\n              View All Templates\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;AAiBA,kCAAkC;AAClC,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAS;YAAc;SAAW;IAC3C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAW;YAAU;SAAa;IAC3C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAQ;YAAO;SAAa;IACrC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAU;YAAQ;SAAa;IACxC;CACD;AAEM,MAAM,oBAAoB;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,IAAI;gBACF,kFAAkF;gBAClF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,cACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;gBAER,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;gBACpC,IAAI,mBAAmB,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBACpD,IAAI,IAAI,EAAE;wBACV,GAAG,IAAI,IAAI,EAAE;oBACf,CAAC;gBAED,yDAAyD;gBACzD,mBAAmB,iBAChB,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,IAAI,CAAC,GACrD,KAAK,CAAC,GAAG;gBAEZ,aAAa,iBAAiB,MAAM,GAAG,IAAI,mBAAmB;YAChE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,aAAa;YACf,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,+DAA+D;QAC/D,IAAI,CAAC,UAAU,eAAe,CAAC,UAAU,UAAU;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzB,QAAQ,KAAK,GAAG;gBAChB,WAAW,KAAK,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,WAAW,GAAG,SAAS,WAAW,IAAI,KAAK,CAAC,EAAE,SAAS,WAAW,EAAE;gBACpE,SAAS,CAAC,cAAc,EAAE,SAAS,KAAK,EAAE;gBAC1C,SAAS,CAAC,0BAA0B,EAAE,SAAS,KAAK,CAAC,6FAA6F,CAAC;gBACnJ,MAAM;gBACN,YAAY,SAAS,EAAE;gBACvB,eAAe,SAAS,KAAK;gBAC7B,QAAQ;YACV;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,+DAA+D;QAC/D,IAAI,CAAC,UAAU,eAAe,CAAC,UAAU,UAAU;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzB,QAAQ,KAAK,GAAG;gBAChB,WAAW,KAAK,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,WAAW,GAAG,SAAS,WAAW,IAAI,KAAK,CAAC,EAAE,SAAS,WAAW,EAAE;gBACpE,SAAS,CAAC,qBAAqB,EAAE,SAAS,KAAK,EAAE;gBACjD,SAAS,CAAC,iCAAiC,EAAE,SAAS,KAAK,CAAC,oEAAoE,CAAC;gBACjI,MAAM;gBACN,YAAY,SAAS,EAAE;gBACvB,eAAe,SAAS,KAAK;gBAC7B,QAAQ;YACV;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;gBAMxD,wBACC,8OAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC,gIAAA,CAAA,OAAI;4BAAS,WAAU;;8CACtB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BANR;;;;;;;;;yCAYf,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,gIAAA,CAAA,OAAI;4BAAmB,WAAU;;8CAEhC,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,QAAQ,iBAChB,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,SAAS,QAAQ;4CACtB,KAAK,SAAS,KAAK;4CACnB,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,SAAS,CAAC;gDACR,MAAM,SAAS,EAAE,MAAM;gDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;4CACzB;4CACA,WAAW;;;;;mDAEX;wCACH,CAAC,SAAS,QAAQ,kBACjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEAAyD;;;;;;;;;;;;;;;;;sDAM5E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc;;kEAEzC,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAuC;;;;;;;;;;;;sDAM1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,SAAS,QAAQ;;;;;;;;;;;sDAKtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;0DAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAMvB,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,SAAS,KAAK;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;0EAAkC,SAAS,MAAM,IAAI;;;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;;;;;;;;;;;;8CAK3B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAU;;;;;;8DAC7C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAU;;;;;;8DAC7C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAU;;;;;;;;;;;;sDAI/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,SAAS,QAAQ,IAAI,SAAS,QAAQ,iBACrC,8OAAC;oEAAK,WAAU;;wEAAgD;wEAC5D,SAAS,QAAQ;wEAAC;wEAAK,SAAS,QAAQ;;;;;;yFAG5C,8OAAC;oEAAK,WAAU;;wEAAgD;wEAAE,SAAS,KAAK;;;;;;;;;;;;;sEAGpF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAA6C;wEAAE,KAAK,KAAK,CAAC,SAAS,KAAK,GAAG;;;;;;;8EAC1F,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;;;;;;;;;;;;;8DAKxD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc;;sFAEzC,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAmB;;;;;;sFACnC,8OAAC;4EAAK,WAAU;sFAAY;;;;;;;;;;;;8EAG9B,8OAAC,kIAAA,CAAA,SAAM;oEACL,WAAU;oEACV,SAAS,IAAM,iBAAiB;;sFAEhC,8OAAC,wNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;sFACzB,8OAAC;4EAAK,WAAU;sFAAmB;;;;;;sFACnC,8OAAC;4EAAK,WAAU;sFAAY;;;;;;;;;;;;;;;;;;sEAIhC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,qBAAqB;;8EAEpC,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;8EACzB,8OAAC;oEAAK,WAAU;8EAAmB;;;;;;8EACnC,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;;;;;;;8DAKhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;;wEAAoB,CAAC,SAAS,SAAS,IAAI,IAAI,EAAE,cAAc;wEAAG;;;;;;;8EAClF,8OAAC;oEAAK,WAAU;8EAAa,CAAC,SAAS,SAAS,IAAI,IAAI,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC,SAAS,SAAS,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,GAAG,SAAS,SAAS;;;;;;;;;;;;sEAE/I,8OAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,8OAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;2BAnJzB,SAAS,EAAE;;;;;;;;;;8BA6J5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,SAAQ;kCAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAa;8CAEtB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/CategoriesSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { \n  Monitor, \n  ShoppingCart, \n  Briefcase, \n  Users, \n  FileText, \n  Smartphone,\n  ArrowRight \n} from 'lucide-react';\n\n// Mock data for categories\nconst categories = [\n  {\n    id: '1',\n    name: 'Dashboard',\n    description: 'Admin panels and data visualization templates',\n    icon: Monitor,\n    templateCount: 45,\n    color: 'bg-blue-500'\n  },\n  {\n    id: '2',\n    name: 'E-commerce',\n    description: 'Online store and shopping cart templates',\n    icon: ShoppingCart,\n    templateCount: 32,\n    color: 'bg-green-500'\n  },\n  {\n    id: '3',\n    name: 'Portfolio',\n    description: 'Creative showcases for professionals',\n    icon: Briefcase,\n    templateCount: 28,\n    color: 'bg-purple-500'\n  },\n  {\n    id: '4',\n    name: 'Landing Page',\n    description: 'High-converting marketing pages',\n    icon: FileText,\n    templateCount: 56,\n    color: 'bg-orange-500'\n  },\n  {\n    id: '5',\n    name: 'Corporate',\n    description: 'Business and company websites',\n    icon: Users,\n    templateCount: 23,\n    color: 'bg-indigo-500'\n  },\n  {\n    id: '6',\n    name: 'Mobile App',\n    description: 'Mobile application UI templates',\n    icon: Smartphone,\n    templateCount: 19,\n    color: 'bg-pink-500'\n  }\n];\n\nexport const CategoriesSection = () => {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Template Categories\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Choose from our organized collection of professional templates\n          </p>\n        </div>\n\n        {/* Categories Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\n          {categories.map((category) => {\n            const IconComponent = category.icon;\n            return (\n              <Link key={category.id} href={`/templates?category=${category.name}`}>\n                <Card className=\"group hover:shadow-lg transition-shadow duration-200 h-full cursor-pointer\">\n                  <CardContent className=\"p-6 text-center\">\n                    <div className={`inline-flex items-center justify-center w-12 h-12 ${category.color} rounded-lg mb-4`}>\n                      <IconComponent className=\"h-6 w-6 text-white\" />\n                    </div>\n\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {category.name}\n                    </h3>\n\n                    <p className=\"text-gray-600 text-sm mb-3\">\n                      {category.description}\n                    </p>\n\n                    <div className=\"text-sm text-gray-500\">\n                      <span>{category.templateCount} templates</span>\n                    </div>\n                  </CardContent>\n                </Card>\n              </Link>\n            );\n          })}\n        </div>\n\n        {/* View All Categories Button */}\n        <div className=\"text-center\">\n          <Button asChild size=\"lg\" variant=\"outline\">\n            <Link href=\"/categories\">\n              View All Categories\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAgBA,2BAA2B;AAC3B,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,wMAAA,CAAA,UAAO;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,sNAAA,CAAA,eAAY;QAClB,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,4MAAA,CAAA,YAAS;QACf,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,8MAAA,CAAA,WAAQ;QACd,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,8MAAA,CAAA,aAAU;QAChB,eAAe;QACf,OAAO;IACT;CACD;AAEM,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,gBAAgB,SAAS,IAAI;wBACnC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAAmB,MAAM,CAAC,oBAAoB,EAAE,SAAS,IAAI,EAAE;sCAClE,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAW,CAAC,kDAAkD,EAAE,SAAS,KAAK,CAAC,gBAAgB,CAAC;sDACnG,cAAA,8OAAC;gDAAc,WAAU;;;;;;;;;;;sDAG3B,8OAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;sDAGhB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAGvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;oDAAM,SAAS,aAAa;oDAAC;;;;;;;;;;;;;;;;;;;;;;;2BAhB3B,SAAS,EAAE;;;;;oBAsB1B;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,SAAQ;kCAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAc;8CAEvB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 1642, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/StatsSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { \n  Download, \n  Star, \n  Users, \n  Globe,\n  ArrowRight,\n  CheckCircle \n} from 'lucide-react';\n\nconst stats = [\n  {\n    icon: Download,\n    value: '50,000+',\n    label: 'Total Downloads',\n    description: 'Templates downloaded by developers worldwide'\n  },\n  {\n    icon: Star,\n    value: '4.9/5',\n    label: 'Average Rating',\n    description: 'Based on 10,000+ customer reviews'\n  },\n  {\n    icon: Users,\n    value: '15,000+',\n    label: 'Happy Customers',\n    description: 'Satisfied developers and designers'\n  },\n  {\n    icon: Globe,\n    value: '120+',\n    label: 'Countries',\n    description: 'Global reach across all continents'\n  }\n];\n\nconst features = [\n  'Premium quality designs',\n  'Regular updates and support',\n  'Mobile-responsive layouts',\n  'Clean, modern code',\n  'Comprehensive documentation',\n  'Lifetime access'\n];\n\nexport const StatsSection = () => {\n  return (\n    <section className=\"py-12 sm:py-16 bg-blue-600 text-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl sm:text-3xl font-bold mb-4\">\n            Ready to Get Started?\n          </h2>\n          <p className=\"text-base sm:text-lg lg:text-xl text-blue-100 mb-6 sm:mb-8 max-w-2xl mx-auto\">\n            Join thousands of businesses using our professional templates to build amazing websites.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center\">\n            <Button asChild size=\"lg\" className=\"bg-white text-blue-600 hover:bg-blue-50 px-6 sm:px-8\">\n              <Link href=\"/templates\">\n                Browse Templates\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"border-white text-white hover:bg-white/10 px-6 sm:px-8\">\n              <Link href=\"/contact\">\n                Contact Us\n              </Link>\n            </Button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AAAA;AAAA;AAAA;AANA;;;;;AAeA,MAAM,QAAQ;IACZ;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,8OAAC;wBAAE,WAAU;kCAA+E;;;;;;kCAI5F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,MAAK;gCAAK,WAAU;0CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAa;;;;;;;;;;;0CAI1B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC", "debugId": null}}]}