exports.id=489,exports.ids=[489],exports.modules={1188:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>n});var r=s(60687),a=s(43210);let i=(0,a.createContext)(void 0),n=({children:e})=>{let[t,s]=(0,a.useState)("light");return(0,a.useEffect)(()=>{let e=localStorage.getItem("theme");e?s(e):s(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")},[]),(0,a.useEffect)(()=>{let e=document.documentElement;"dark"===t?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("theme",t)},[t]),(0,r.jsx)(i.Provider,{value:{theme:t,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")}},children:e})}},4482:(e,t,s)=>{"use strict";s.d(t,{Navbar:()=>H});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),o=s(63213),l=s(29523),d=s(26312),c=s(4780);function m({...e}){return(0,r.jsx)(d.bL,{"data-slot":"dropdown-menu",...e})}function h({...e}){return(0,r.jsx)(d.l9,{"data-slot":"dropdown-menu-trigger",...e})}function x({className:e,sideOffset:t=4,...s}){return(0,r.jsx)(d.ZL,{children:(0,r.jsx)(d.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function u({className:e,inset:t,variant:s="default",...a}){return(0,r.jsx)(d.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function f({className:e,...t}){return(0,r.jsx)(d.wv,{"data-slot":"dropdown-menu-separator",className:(0,c.cn)("bg-border -mx-1 my-1 h-px",e),...t})}var p=s(11096);function v({className:e,...t}){return(0,r.jsx)(p.bL,{"data-slot":"avatar",className:(0,c.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function g({className:e,...t}){return(0,r.jsx)(p.H4,{"data-slot":"avatar-fallback",className:(0,c.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var b=s(26134),j=s(11860);function y({...e}){return(0,r.jsx)(b.bL,{"data-slot":"sheet",...e})}function N({...e}){return(0,r.jsx)(b.l9,{"data-slot":"sheet-trigger",...e})}function w({...e}){return(0,r.jsx)(b.ZL,{"data-slot":"sheet-portal",...e})}function C({className:e,...t}){return(0,r.jsx)(b.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function A({className:e,children:t,side:s="right",...a}){return(0,r.jsxs)(w,{children:[(0,r.jsx)(C,{}),(0,r.jsxs)(b.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...a,children:[t,(0,r.jsxs)(b.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(j.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var k=s(32192),P=s(10022),S=s(98971),z=s(48340),T=s(58869),I=s(84027),D=s(71057),E=s(40083),F=s(12941),L=s(96474);let H=()=>{let{user:e,userData:t,logout:s}=(0,o.A)(),[i,d]=(0,a.useState)(!1),c=async()=>{try{await s()}catch(e){console.error("Error logging out:",e)}};return(0,r.jsx)("nav",{className:"border-b bg-white shadow-sm sticky top-0 z-50",children:(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex h-14 sm:h-16 items-center justify-between",children:[(0,r.jsx)(n(),{href:"/",className:"flex items-center",children:(0,r.jsx)("span",{className:"font-bold text-lg sm:text-xl text-gray-900",children:"KaleidoneX"})}),(0,r.jsx)("div",{className:"hidden lg:flex items-center space-x-6 xl:space-x-8",children:(0,r.jsx)(()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n(),{href:"/",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center",children:[(0,r.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Home"]}),(0,r.jsxs)(n(),{href:"/templates",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center",children:[(0,r.jsx)(P.A,{className:"w-4 h-4 mr-2"}),"Templates"]}),(0,r.jsxs)(n(),{href:"/custom-request",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center",children:[(0,r.jsx)(S.A,{className:"w-4 h-4 mr-2"}),"Customize"]}),(0,r.jsxs)(n(),{href:"/contact",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center",children:[(0,r.jsx)(z.A,{className:"w-4 h-4 mr-2"}),"Contact"]})]}),{})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e?(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(m,{children:[(0,r.jsx)(h,{asChild:!0,children:(0,r.jsx)(l.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,r.jsx)(v,{className:"h-8 w-8",children:(0,r.jsx)(g,{children:t?.displayName?.[0]||e.email?.[0]?.toUpperCase()})})})}),(0,r.jsxs)(x,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsx)(u,{asChild:!0,children:(0,r.jsxs)(n(),{href:t?.role==="admin"?"/admin":"/dashboard",className:"flex items-center",children:[(0,r.jsx)(T.A,{className:"mr-2 h-4 w-4"}),t?.role==="admin"?"Admin Dashboard":"Dashboard"]})}),(0,r.jsx)(u,{asChild:!0,children:(0,r.jsxs)(n(),{href:"/profile",className:"flex items-center",children:[(0,r.jsx)(I.A,{className:"mr-2 h-4 w-4"}),"Profile Settings"]})}),(0,r.jsx)(u,{asChild:!0,children:(0,r.jsxs)(n(),{href:"/orders",className:"flex items-center",children:[(0,r.jsx)(D.A,{className:"mr-2 h-4 w-4"}),"My Orders"]})}),(0,r.jsx)(f,{}),(0,r.jsxs)(u,{onClick:c,children:[(0,r.jsx)(E.A,{className:"mr-2 h-4 w-4"}),"Log out"]})]})]})}):(0,r.jsx)("div",{className:"hidden lg:flex items-center space-x-2",children:(0,r.jsx)(l.$,{asChild:!0,variant:"ghost",className:"text-gray-700 hover:text-gray-900 text-sm",children:(0,r.jsxs)(n(),{href:"/auth",className:"flex items-center",children:[(0,r.jsx)(T.A,{className:"w-4 h-4 mr-2"}),"Sign In"]})})}),(0,r.jsxs)(y,{open:i,onOpenChange:d,children:[(0,r.jsx)(N,{asChild:!0,className:"lg:hidden",children:(0,r.jsx)(l.$,{variant:"ghost",size:"sm",className:"p-2",children:(0,r.jsx)(F.A,{className:"h-5 w-5"})})}),(0,r.jsx)(A,{side:"right",className:"w-[280px] sm:w-[350px]",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-4 mt-6",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,r.jsxs)(n(),{href:"/",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>d(!1),children:[(0,r.jsx)(k.A,{className:"w-4 h-4 mr-3"}),"Home"]}),(0,r.jsxs)(n(),{href:"/templates",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>d(!1),children:[(0,r.jsx)(P.A,{className:"w-4 h-4 mr-3"}),"Templates"]}),(0,r.jsxs)(n(),{href:"/custom-request",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>d(!1),children:[(0,r.jsx)(S.A,{className:"w-4 h-4 mr-3"}),"Customize"]}),(0,r.jsxs)(n(),{href:"/contact",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>d(!1),children:[(0,r.jsx)(z.A,{className:"w-4 h-4 mr-3"}),"Contact"]})]}),e?(0,r.jsx)("div",{className:"flex flex-col space-y-3 pt-4 border-t",children:(0,r.jsxs)("button",{onClick:()=>{s(),d(!1)},className:"text-left text-sm font-medium text-red-600 hover:text-red-700 transition-colors flex items-center py-2",children:[(0,r.jsx)(E.A,{className:"w-4 h-4 mr-3"}),"Sign Out"]})}):(0,r.jsxs)("div",{className:"flex flex-col space-y-3 pt-4 border-t",children:[(0,r.jsxs)(n(),{href:"/auth",className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2",onClick:()=>d(!1),children:[(0,r.jsx)(T.A,{className:"w-4 h-4 mr-3"}),"Sign In"]}),(0,r.jsxs)(n(),{href:"/auth?mode=signup",className:"text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors flex items-center py-2",onClick:()=>d(!1),children:[(0,r.jsx)(L.A,{className:"w-4 h-4 mr-3"}),"Get Started"]})]})]})})]})]})]})})})}},4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var r=s(49384),a=s(82348);function i(...e){return(0,a.QP)((0,r.$)(e))}},9835:(e,t,s)=>{Promise.resolve().then(s.bind(s,52581)),Promise.resolve().then(s.bind(s,51317)),Promise.resolve().then(s.bind(s,4482)),Promise.resolve().then(s.bind(s,63213)),Promise.resolve().then(s.bind(s,1188))},16868:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},28659:(e,t,s)=>{"use strict";s.d(t,{Footer:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Footer.tsx","Footer")},29131:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx","useAuth");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx","AuthProvider")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:i=!1,...l}){let d=i?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:s,className:e})),...l})}},30004:(e,t,s)=>{"use strict";s.d(t,{Navbar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Navbar.tsx","Navbar")},33784:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,j:()=>o});var r=s(67989),a=s(50227),i=s(75535);let n=(0,r.Wp)({apiKey:"AIzaSyDOM-aLk-COF75INOBznnjfIYELAviTp7s",authDomain:"kaleidonex.firebaseapp.com",projectId:"kaleidonex",storageBucket:"kaleidonex.firebasestorage.app",messagingSenderId:"150841800996",appId:"1:150841800996:web:6a9bae44f909534fe4dbb1"}),o=(0,a.xI)(n),l=(0,i.aU)(n)},51317:(e,t,s)=>{"use strict";s.d(t,{Footer:()=>v});var r=s(60687);s(43210);var a=s(85814),i=s.n(a),n=s(29523),o=s(89667),l=s(19526),d=s(72575),c=s(66232),m=s(98876),h=s(62157),x=s(41550),u=s(48340),f=s(97992),p=s(67760);let v=()=>(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-xl font-bold",children:"KaleidoneX"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Premium templates and digital solutions for modern businesses. Create stunning websites with our professionally designed templates."}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,r.jsx)(l.A,{className:"h-5 w-5"})}),(0,r.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,r.jsx)(c.A,{className:"h-5 w-5"})}),(0,r.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,r.jsx)(m.A,{className:"h-5 w-5"})}),(0,r.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:(0,r.jsx)(h.A,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold",children:"Quick Links"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/",className:"text-gray-400 hover:text-white transition-colors",children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/templates",className:"text-gray-400 hover:text-white transition-colors",children:"Templates"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/categories",className:"text-gray-400 hover:text-white transition-colors",children:"Categories"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/about",className:"text-gray-400 hover:text-white transition-colors",children:"About Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/contact",className:"text-gray-400 hover:text-white transition-colors",children:"Contact"})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold",children:"Support"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/help",className:"text-gray-400 hover:text-white transition-colors",children:"Help Center"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/documentation",className:"text-gray-400 hover:text-white transition-colors",children:"Documentation"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/faq",className:"text-gray-400 hover:text-white transition-colors",children:"FAQ"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/privacy",className:"text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/terms",className:"text-gray-400 hover:text-white transition-colors",children:"Terms of Service"})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold",children:"Stay Updated"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Subscribe to our newsletter for the latest templates and updates."}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.p,{type:"email",placeholder:"Enter your email",className:"bg-gray-800 border-gray-700 text-white placeholder-gray-400"}),(0,r.jsx)(n.$,{className:"w-full bg-blue-600 hover:bg-blue-700",children:"Subscribe"})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-800 pt-8 mb-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"+1 (555) 123-4567"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"San Francisco, CA"})]})]})}),(0,r.jsx)("div",{className:"border-t border-gray-800 pt-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"\xa9 2024 KaleidoneX. All rights reserved."}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-400",children:[(0,r.jsx)("span",{children:"Made with"}),(0,r.jsx)(p.A,{className:"h-4 w-4 text-red-500 fill-current"}),(0,r.jsx)("span",{children:"by KaleidoneX Team"})]})]})})]})})},61135:()=>{},63213:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,AuthProvider:()=>c});var r=s(60687),a=s(43210),i=s(50227),n=s(75535),o=s(33784);let l=(0,a.createContext)(void 0),d=()=>{let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=({children:e})=>{let[t,s]=(0,a.useState)(null),[d,c]=(0,a.useState)(null),[m,h]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,i.hg)(o.j,async e=>{if(e){s(e);try{let t=await (0,n.x7)((0,n.H9)(o.db,"users",e.uid));if(t.exists())c(t.data());else{let t={uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(o.db,"users",e.uid),t),c(t)}}catch(t){console.error("Error fetching/creating user data:",t),c({uid:e.uid,email:e.email,role:"user",displayName:e.displayName||"",createdAt:new Date})}}else s(null),c(null);h(!1)});return()=>e()},[]);let x=async(e,t)=>{try{await (0,i.x9)(o.j,e,t)}catch(e){throw console.error("Sign in error:",e),e}},u=async(e,t)=>{try{let{user:s}=await (0,i.eJ)(o.j,e,t),r={uid:s.uid,email:s.email,role:"user",displayName:s.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(o.db,"users",s.uid),r)}catch(e){throw console.error("Sign up error:",e),e}},f=async()=>{await (0,i.CI)(o.j)},p=async e=>{if(!t)throw Error("No user logged in");try{let s={...e,updatedAt:new Date};await (0,n.BN)((0,n.H9)(o.db,"users",t.uid),s,{merge:!0}),d&&c({...d,...s})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,r.jsx)(l.Provider,{value:{user:t,userData:d,loading:m,signIn:x,signUp:u,logout:f,updateUserProfile:p},children:e})}},68462:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/ThemeContext.tsx","useTheme");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/ThemeContext.tsx","ThemeProvider")},72883:(e,t,s)=>{Promise.resolve().then(s.bind(s,6931)),Promise.resolve().then(s.bind(s,28659)),Promise.resolve().then(s.bind(s,30004)),Promise.resolve().then(s.bind(s,29131)),Promise.resolve().then(s.bind(s,68462))},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>x});var r=s(37413),a=s(2202),i=s.n(a),n=s(64988),o=s.n(n);s(61135);var l=s(29131),d=s(68462),c=s(30004),m=s(28659),h=s(6931);let x={title:"KaleidoneX - Premium Template Marketplace",description:"Discover and purchase premium templates for your projects"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:(0,r.jsx)(d.ThemeProvider,{children:(0,r.jsxs)(l.AuthProvider,{children:[(0,r.jsx)(c.Navbar,{}),(0,r.jsx)("main",{className:"min-h-screen",children:e}),(0,r.jsx)(m.Footer,{}),(0,r.jsx)(h.Toaster,{position:"top-right"})]})})})})}},98308:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))}};