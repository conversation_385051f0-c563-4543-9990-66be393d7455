(()=>{var e={};e.id=582,e.ids=[582],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3432:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/success/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/success/page.tsx","default")},5245:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(60687);t(43210);var a=t(44493),i=t(29523),n=t(96834),o=t(5336);let l=(0,t(62688).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var c=t(64398),d=t(31158),u=t(70334),x=t(85814),m=t.n(x),p=t(16189);function h(){return(0,p.useRouter)(),(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"max-w-2xl w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative inline-block",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse",children:(0,r.jsx)(o.A,{className:"h-12 w-12 text-green-600"})}),(0,r.jsx)("div",{className:"absolute -top-2 -right-2",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center animate-bounce",children:(0,r.jsx)(l,{className:"h-4 w-4 text-yellow-600"})})})]}),(0,r.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"\uD83C\uDF89 Purchase Successful!"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"Thank you for your purchase! Your template is ready for download."})]}),(0,r.jsxs)(a.Zp,{className:"border-0 shadow-xl",children:[(0,r.jsxs)(a.aR,{className:"text-center pb-4",children:[(0,r.jsx)(a.ZB,{className:"text-xl",children:"Your Template is Ready!"}),(0,r.jsx)(a.BT,{children:"You can now download and start using your premium template"})]}),(0,r.jsxs)(a.Wu,{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:"Creative Portfolio Template"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Professional portfolio design"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"₹1,999"}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-yellow-500",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 fill-current"}),(0,r.jsx)("span",{className:"text-sm",children:"4.9"})]})]})]})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"What's Included:"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:"Source Files"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:"Documentation"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:"Lifetime Updates"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm",children:"Premium Support"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.$,{className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white h-12",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"Download Template"]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsx)(i.$,{variant:"outline",asChild:!0,children:(0,r.jsx)(m(),{href:"/dashboard",children:"View Dashboard"})}),(0,r.jsx)(i.$,{variant:"outline",asChild:!0,children:(0,r.jsxs)(m(),{href:"/templates",children:["Browse More",(0,r.jsx)(u.A,{className:"h-4 w-4 ml-2"})]})})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Next Steps:"}),(0,r.jsxs)("ol",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"1. Download your template files"}),(0,r.jsx)("li",{children:"2. Read the documentation"}),(0,r.jsx)("li",{children:"3. Customize to your needs"}),(0,r.jsx)("li",{children:"4. Launch your project!"})]})]}),(0,r.jsxs)("div",{className:"text-center text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:["Need help? ",(0,r.jsx)(m(),{href:"/contact",className:"text-blue-600 hover:underline",children:"Contact our support team"})]}),(0,r.jsx)("p",{className:"mt-2",children:"Redirecting to dashboard in 10 seconds..."})]})]})]}),(0,r.jsx)(a.Zp,{className:"border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50",children:(0,r.jsxs)(a.Wu,{className:"p-6 text-center",children:[(0,r.jsxs)(n.E,{className:"mb-3 bg-yellow-500",children:[(0,r.jsx)(l,{className:"h-4 w-4 mr-1"}),"Special Offer"]}),(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Get 20% off your next purchase!"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Use code ",(0,r.jsx)("code",{className:"bg-yellow-200 px-2 py-1 rounded font-mono",children:"WELCOME20"})," on your next template purchase"]}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsx)(m(),{href:"/templates",children:"Shop More Templates"})})]})})]})})}},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27643:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c={children:["",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3432)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/success/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/Documents/augment-projects/kaleidonex/src/app/success/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/success/page",pathname:"/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32230:(e,s,t)=>{Promise.resolve().then(t.bind(t,5245))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n});var r=t(60687);t(43210);var a=t(4780);function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function o({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function l({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function c({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70334:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92062:(e,s,t)=>{Promise.resolve().then(t.bind(t,3432))},94735:e=>{"use strict";e.exports=require("events")},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,asChild:t=!1,...i}){let l=t?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:s}),e),...i})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,415,658,489],()=>t(27643));module.exports=r})();