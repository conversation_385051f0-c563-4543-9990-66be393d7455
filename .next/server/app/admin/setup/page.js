(()=>{var e={};e.id=912,e.ids=[912],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23034:(e,t,a)=>{Promise.resolve().then(a.bind(a,41922))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},41312:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41862:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},41922:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(60687),r=a(43210),i=a(29523),o=a(44493),n=a(91821),d=a(61611),l=a(5336),c=a(41862),p=a(41312),m=a(75535),u=a(33784);let h=[{title:"Modern Dashboard",description:"Clean and modern dashboard template with analytics and data visualization",category:"Dashboard",price:49,imageUrl:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["React","TypeScript","Charts","Analytics"],featured:!0,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"E-commerce Store",description:"Complete e-commerce solution with shopping cart and payment integration",category:"E-commerce",price:79,imageUrl:"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["Next.js","Stripe","Shopping Cart","Responsive"],featured:!0,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"Landing Page Pro",description:"High-converting landing page template for SaaS and startups",category:"Landing Page",price:39,imageUrl:"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["HTML","CSS","JavaScript","Conversion"],featured:!0,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"Portfolio Showcase",description:"Creative portfolio template for designers and developers",category:"Portfolio",price:29,imageUrl:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["Vue.js","GSAP","Animation","Creative"],featured:!1,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"Corporate Website",description:"Professional corporate website template with multiple pages",category:"Corporate",price:59,imageUrl:"https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["WordPress","PHP","Corporate","Professional"],featured:!1,createdAt:new Date,updatedAt:new Date,createdBy:"admin"},{title:"Mobile App UI",description:"Complete mobile app UI kit with 50+ screens",category:"Mobile App",price:69,imageUrl:"https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop",previewUrl:"#",downloadUrl:"#",tags:["React Native","Flutter","Mobile","UI Kit"],featured:!1,createdAt:new Date,updatedAt:new Date,createdBy:"admin"}],g=[{name:"Dashboard",description:"Admin panels and data visualization templates",imageUrl:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",templateCount:0},{name:"E-commerce",description:"Online stores and shopping cart templates",imageUrl:"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",templateCount:0},{name:"Landing Page",description:"High-converting marketing pages",imageUrl:"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",templateCount:0},{name:"Portfolio",description:"Creative showcases for professionals",imageUrl:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop",templateCount:0},{name:"Corporate",description:"Business and company websites",imageUrl:"https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop",templateCount:0},{name:"Mobile App",description:"Mobile application UI templates",imageUrl:"https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop",templateCount:0}],x=async()=>{try{for(let e of(console.log("Adding sample templates..."),h))await (0,m.gS)((0,m.rJ)(u.db,"templates"),e);for(let e of(console.log("Adding sample categories..."),g))await (0,m.gS)((0,m.rJ)(u.db,"categories"),e);for(let e of(console.log("Adding sample custom requests..."),f))await (0,m.gS)((0,m.rJ)(u.db,"customRequests"),e);for(let e of(console.log("Adding sample users..."),w)){let t=(0,m.H9)((0,m.rJ)(u.db,"users"));await (0,m.BN)(t,{...e,uid:t.id})}for(let e of(console.log("Adding sample contact messages..."),y))await (0,m.gS)((0,m.rJ)(u.db,"contactMessages"),e);console.log("All sample data added successfully!")}catch(e){throw console.error("Error adding sample data:",e),e}},f=[{userId:"user1",userEmail:"<EMAIL>",title:"Modern SaaS Dashboard",description:"I need a modern dashboard for my SaaS application with analytics, user management, and billing features.",category:"Dashboard",budget:800,deadline:new Date("2024-02-15"),status:"pending",createdAt:new Date("2024-01-10"),updatedAt:new Date("2024-01-10")},{userId:"user2",userEmail:"<EMAIL>",title:"E-commerce Mobile App",description:"Looking for a mobile-first e-commerce template with product catalog, shopping cart, and payment integration.",category:"E-commerce",budget:1200,deadline:new Date("2024-02-20"),status:"in-progress",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-12"),adminNotes:"Started working on wireframes and design mockups."},{userId:"user3",userEmail:"<EMAIL>",title:"Portfolio Website",description:"Creative portfolio website for a photographer with gallery, blog, and contact features.",category:"Portfolio",budget:500,status:"completed",createdAt:new Date("2024-01-05"),updatedAt:new Date("2024-01-15"),adminNotes:"Completed and delivered. Client very satisfied."},{userId:"user4",userEmail:"<EMAIL>",title:"Corporate Landing Page",description:"Professional landing page for a consulting firm with services showcase and lead generation forms.",category:"Landing Page",budget:600,deadline:new Date("2024-02-10"),status:"pending",createdAt:new Date("2024-01-12"),updatedAt:new Date("2024-01-12")},{userId:"user5",userEmail:"<EMAIL>",title:"Restaurant Website",description:"Website for a restaurant with menu display, online ordering, and reservation system.",category:"Other",budget:900,status:"cancelled",createdAt:new Date("2024-01-03"),updatedAt:new Date("2024-01-07"),adminNotes:"Client cancelled due to budget constraints."}],w=[{email:"<EMAIL>",role:"user",fullName:"John Doe",displayName:"John",phoneNumber:"1234567890",countryCode:"+1",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-10")},{email:"<EMAIL>",role:"user",fullName:"Sarah Smith",displayName:"Sarah",phoneNumber:"9876543210",countryCode:"+1",createdAt:new Date("2024-01-02"),updatedAt:new Date("2024-01-08")},{email:"<EMAIL>",role:"user",fullName:"Mike Johnson",displayName:"Mike",phoneNumber:"5555555555",countryCode:"+44",createdAt:new Date("2024-01-03"),updatedAt:new Date("2024-01-05")},{email:"<EMAIL>",role:"user",fullName:"Lisa Brown",displayName:"Lisa",phoneNumber:"7777777777",countryCode:"+91",createdAt:new Date("2024-01-04"),updatedAt:new Date("2024-01-12")},{email:"<EMAIL>",role:"user",fullName:"David Wilson",displayName:"David",phoneNumber:"3333333333",countryCode:"+61",createdAt:new Date("2024-01-05"),updatedAt:new Date("2024-01-07")}],y=[{name:"Alex Thompson",email:"<EMAIL>",subject:"Question about custom development",message:"Hi, I'm interested in getting a custom template developed. Can you provide more information about your process and pricing?",status:"unread",createdAt:new Date("2024-01-14")},{name:"Emma Davis",email:"<EMAIL>",subject:"Template customization request",message:"I purchased the Modern Dashboard template and would like to customize the color scheme. Is this service available?",status:"read",createdAt:new Date("2024-01-13")},{name:"Ryan Miller",email:"<EMAIL>",subject:"Technical support needed",message:"I'm having trouble setting up the e-commerce template. The payment integration is not working as expected.",status:"replied",createdAt:new Date("2024-01-12")}];var v=a(63213);function b(){let[e,t]=(0,r.useState)(!1),[a,m]=(0,r.useState)(!1),[u,h]=(0,r.useState)(""),[g,f]=(0,r.useState)(!1),[w,y]=(0,r.useState)(!1),[b,j]=(0,r.useState)(""),[A,N]=(0,r.useState)(""),{user:D}=(0,v.A)(),C=async()=>{t(!0),h(""),m(!1);try{await x(),m(!0)}catch(e){h(e.message||"Failed to add sample data")}finally{t(!1)}};return(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Setup"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Set up your KaleidoneX marketplace with sample data"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"Sample Data"]}),(0,s.jsx)(o.BT,{children:"Add sample templates and categories to get started quickly"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["This will add:",(0,s.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,s.jsx)("li",{children:"6 sample templates across different categories"}),(0,s.jsx)("li",{children:"6 template categories"}),(0,s.jsx)("li",{children:"Professional images from Unsplash"})]})]}),u&&(0,s.jsx)(n.Fc,{variant:"destructive",children:(0,s.jsx)(n.TN,{children:u})}),a&&(0,s.jsxs)(n.Fc,{children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(n.TN,{children:"Sample data added successfully! You can now browse templates on your site."})]}),(0,s.jsxs)(i.$,{onClick:C,disabled:e||a,className:"w-full",children:[e&&(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),a?"Sample Data Added":"Add Sample Data"]})]})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsxs)(o.ZB,{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"Next Steps"]})}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4 text-sm text-gray-600",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"1. Make yourself an admin"}),(0,s.jsx)("p",{children:'Go to Firebase Console → Firestore → users collection → find your user → change role to "admin"'})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"2. Test the application"}),(0,s.jsx)("p",{children:"Browse templates, test the search and filtering functionality"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"3. Customize templates"}),(0,s.jsx)("p",{children:"Replace sample templates with your own designs and content"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"4. Set up payments"}),(0,s.jsx)("p",{children:"Integrate Stripe or your preferred payment processor"})]})]})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"Firebase Configuration"})}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Authentication"}),(0,s.jsx)("span",{className:"text-green-600 font-medium",children:"✓ Configured"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Firestore Database"}),(0,s.jsx)("span",{className:"text-green-600 font-medium",children:"✓ Connected"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Security Rules"}),(0,s.jsx)("span",{className:"text-yellow-600 font-medium",children:"⚠ Check manually"})]})]})})]})]})]})})}},44493:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>n,Zp:()=>i,aR:()=>o});var s=a(60687);a(43210);var r=a(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},61611:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},76178:(e,t,a)=>{Promise.resolve().then(a.bind(a,94606))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91067:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=a(65239),r=a(48088),i=a(88170),o=a.n(i),n=a(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(t,d);let l={children:["",{children:["admin",{children:["setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94606)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/setup/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/setup/page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/setup/page",pathname:"/admin/setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>n,TN:()=>d});var s=a(60687);a(43210);var r=a(24224),i=a(4780);let o=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...a}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(o({variant:t}),e),...a})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},94606:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/setup/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/setup/page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,415,658,489],()=>a(91067));module.exports=s})();