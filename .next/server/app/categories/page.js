(()=>{var e={};e.id=379,e.ids=[379],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8911:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/categories/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/categories/page.tsx","default")},9716:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(60687);r(43210);var a=r(85814),o=r.n(a),i=r(44493),n=r(96834),l=r(34318),c=r(28561),d=r(57800),p=r(10022),m=r(41312),u=r(17581);let x=(0,r(62688).A)("newspaper",[["path",{d:"M15 18h-5",key:"95g1m2"}],["path",{d:"M18 14h-8",key:"sponae"}],["path",{d:"M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2",key:"39pd36"}],["rect",{width:"8",height:"4",x:"10",y:"6",rx:"1",key:"aywv1n"}]]);var g=r(11437),h=r(70334);let b=[{id:"1",name:"Dashboard",description:"Admin panels, analytics dashboards, and data visualization templates",icon:l.A,templateCount:45,color:"bg-blue-500",bgColor:"bg-blue-50",textColor:"text-blue-600"},{id:"2",name:"E-commerce",description:"Online stores, shopping carts, product catalogs, and marketplace templates",icon:c.A,templateCount:32,color:"bg-green-500",bgColor:"bg-green-50",textColor:"text-green-600"},{id:"3",name:"Portfolio",description:"Creative showcases for designers, developers, and creative professionals",icon:d.A,templateCount:28,color:"bg-purple-500",bgColor:"bg-purple-50",textColor:"text-purple-600"},{id:"4",name:"Landing Page",description:"High-converting marketing pages, product launches, and promotional sites",icon:p.A,templateCount:56,color:"bg-orange-500",bgColor:"bg-orange-50",textColor:"text-orange-600"},{id:"5",name:"Corporate",description:"Business websites, company profiles, and professional service sites",icon:m.A,templateCount:23,color:"bg-indigo-500",bgColor:"bg-indigo-50",textColor:"text-indigo-600"},{id:"6",name:"Mobile App",description:"Mobile application UI kits, app landing pages, and mobile-first designs",icon:u.A,templateCount:19,color:"bg-pink-500",bgColor:"bg-pink-50",textColor:"text-pink-600"},{id:"7",name:"Blog/CMS",description:"Blog themes, content management systems, and publishing platforms",icon:x,templateCount:34,color:"bg-yellow-500",bgColor:"bg-yellow-50",textColor:"text-yellow-600"},{id:"8",name:"Web App",description:"SaaS applications, productivity tools, and web-based software interfaces",icon:g.A,templateCount:41,color:"bg-teal-500",bgColor:"bg-teal-50",textColor:"text-teal-600"}];function f(){let e=b.reduce((e,t)=>e+t.templateCount,0);return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Template Categories"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto mb-6",children:"Explore our organized collection of premium templates across different categories and industries."}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-6 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"font-semibold text-2xl text-gray-900",children:b.length}),(0,s.jsx)("span",{className:"ml-2",children:"Categories"})]}),(0,s.jsx)("div",{className:"w-px h-6 bg-gray-300"}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"font-semibold text-2xl text-gray-900",children:e}),(0,s.jsx)("span",{className:"ml-2",children:"Total Templates"})]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:b.map(e=>{let t=e.icon;return(0,s.jsx)(o(),{href:`/templates?category=${e.name}`,children:(0,s.jsx)(i.Zp,{className:"group hover:shadow-xl transition-all duration-300 border-0 shadow-lg h-full cursor-pointer overflow-hidden",children:(0,s.jsxs)(i.Wu,{className:"p-0",children:[(0,s.jsxs)("div",{className:`${e.bgColor} p-6 text-center`,children:[(0,s.jsx)("div",{className:`inline-flex items-center justify-center w-16 h-16 ${e.color} rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300`,children:(0,s.jsx)(t,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("h3",{className:`text-xl font-semibold ${e.textColor} mb-2 group-hover:text-opacity-80 transition-colors`,children:e.name}),(0,s.jsxs)(n.E,{variant:"secondary",className:"bg-white/80",children:[e.templateCount," templates"]})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-4",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Explore Templates"}),(0,s.jsx)(h.A,{className:"h-4 w-4 text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all"})]})]})]})})},e.id)})}),(0,s.jsxs)("div",{className:"mt-16",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Most Popular Categories"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Categories with the highest number of downloads and user engagement"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:b.sort((e,t)=>t.templateCount-e.templateCount).slice(0,3).map((e,t)=>{let r=e.icon;return(0,s.jsx)(i.Zp,{className:"relative overflow-hidden",children:(0,s.jsxs)(i.Wu,{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:`p-3 ${e.bgColor} rounded-lg`,children:(0,s.jsx)(r,{className:`h-6 w-6 ${e.textColor}`})}),(0,s.jsxs)(n.E,{className:"bg-gradient-to-r from-blue-600 to-purple-600",children:["#",t+1," Popular"]})]}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.name}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[e.templateCount," templates available"]}),(0,s.jsxs)(o(),{href:`/templates?category=${e.name}`,className:"text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center",children:["Browse",(0,s.jsx)(h.A,{className:"h-3 w-3 ml-1"})]})]})]})},e.id)})})]}),(0,s.jsx)("div",{className:"mt-16 text-center",children:(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Can't Find What You're Looking For?"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Our expert team can create custom templates tailored to your specific needs and industry requirements."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(o(),{href:"/custom-request",className:"inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors",children:["Request Custom Design",(0,s.jsx)(h.A,{className:"ml-2 h-4 w-4"})]}),(0,s.jsx)(o(),{href:"/contact",className:"inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors",children:"Contact Support"})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},17581:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28561:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34318:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},38653:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8911)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/categories/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/Documents/augment-projects/kaleidonex/src/app/categories/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/categories/page",pathname:"/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},41312:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>n,Zp:()=>o,aR:()=>i});var s=r(60687);r(43210);var a=r(4780);function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55983:(e,t,r)=>{Promise.resolve().then(r.bind(r,8911))},57800:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64231:(e,t,r)=>{Promise.resolve().then(r.bind(r,9716))},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),o=r(24224),i=r(4780);let n=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...o}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(n({variant:t}),e),...o})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,415,658,489],()=>r(38653));module.exports=s})();