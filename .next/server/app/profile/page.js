(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9309:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(60687),r=s(43210),n=s(63213),i=s(44493),o=s(29523),l=s(89667),d=s(54300),c=s(15079),u=s(91821),m=s(58869),p=s(41550),x=s(48340),h=s(40228),f=s(11437),g=s(41862);let v=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var y=s(85814),j=s.n(y);let N=[{code:"+1",country:"US/CA"},{code:"+44",country:"UK"},{code:"+91",country:"India"},{code:"+86",country:"China"},{code:"+81",country:"Japan"},{code:"+49",country:"Germany"},{code:"+33",country:"France"},{code:"+39",country:"Italy"},{code:"+34",country:"Spain"},{code:"+61",country:"Australia"},{code:"+55",country:"Brazil"},{code:"+52",country:"Mexico"},{code:"+7",country:"Russia"},{code:"+82",country:"South Korea"},{code:"+65",country:"Singapore"},{code:"+971",country:"UAE"},{code:"+966",country:"Saudi Arabia"},{code:"+27",country:"South Africa"}];function b(){let{user:e,userData:t,updateUserProfile:s}=(0,n.A)(),[y,b]=(0,r.useState)(!1),[w,A]=(0,r.useState)(!1),[k,_]=(0,r.useState)(""),[q,C]=(0,r.useState)({fullName:t?.fullName||"",displayName:t?.displayName||"",phoneNumber:t?.phoneNumber||"",countryCode:t?.countryCode||"+1"});if(!e)return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Please sign in to access your profile"}),(0,a.jsx)(o.$,{asChild:!0,children:(0,a.jsx)(j(),{href:"/auth",children:"Sign In"})})]});let P=e=>{C(t=>({...t,[e.target.name]:e.target.value})),_(""),A(!1)},z=async e=>{if(e.preventDefault(),b(!0),_(""),A(!1),!q.fullName.trim()){_("Full name is required"),b(!1);return}if(q.phoneNumber&&!/^\d{10,15}$/.test(q.phoneNumber.replace(/\s/g,""))){_("Please enter a valid phone number (10-15 digits)"),b(!1);return}try{await s({fullName:q.fullName.trim(),displayName:q.displayName.trim()||q.fullName.trim(),phoneNumber:q.phoneNumber.trim(),countryCode:q.countryCode}),A(!0)}catch(e){_(e.message||"Failed to update profile")}finally{b(!1)}};return(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Manage your account information and preferences"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Account Information"]}),(0,a.jsx)(i.BT,{children:"View your account details and status"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:e.email})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"text-sm font-medium text-gray-600",children:"Phone Number"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:t?.phoneNumber?`${t.countryCode||"+1"} ${t.phoneNumber}`:"Not provided"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"text-sm font-medium text-gray-600",children:"Full Name"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:t?.fullName||"Not provided"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{className:"text-sm font-medium text-gray-600",children:"Member Since"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:t?.createdAt?new Date(t.createdAt.seconds?1e3*t.createdAt.seconds:t.createdAt).toLocaleDateString():"Recently joined"})]})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Personal Information"}),(0,a.jsx)(i.BT,{children:"Update your personal details and contact information"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("form",{onSubmit:z,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"fullName",children:"Full Name *"}),(0,a.jsx)(l.p,{id:"fullName",name:"fullName",type:"text",placeholder:"Enter your full name",value:q.fullName,onChange:P,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"displayName",children:"Display Name"}),(0,a.jsx)(l.p,{id:"displayName",name:"displayName",type:"text",placeholder:"How others see your name",value:q.displayName,onChange:P})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{children:"Phone Number"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(c.l6,{value:q.countryCode,onValueChange:e=>{C(t=>({...t,countryCode:e})),_(""),A(!1)},children:[(0,a.jsxs)(c.bq,{className:"w-32",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2"}),(0,a.jsx)(c.yv,{})]}),(0,a.jsx)(c.gC,{children:N.map(e=>(0,a.jsxs)(c.eb,{value:e.code,children:[e.code," (",e.country,")"]},e.code))})]}),(0,a.jsx)(l.p,{name:"phoneNumber",type:"tel",placeholder:"Enter phone number",value:q.phoneNumber,onChange:P,className:"flex-1"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Enter your phone number without the country code"})]}),k&&(0,a.jsx)(u.Fc,{variant:"destructive",children:(0,a.jsx)(u.TN,{children:k})}),w&&(0,a.jsx)(u.Fc,{className:"border-green-200 bg-green-50",children:(0,a.jsx)(u.TN,{className:"text-green-800",children:"Profile updated successfully!"})}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)(o.$,{type:"submit",disabled:y,children:[y&&(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,a.jsx)(v,{className:"mr-2 h-4 w-4"}),"Save Changes"]}),(0,a.jsx)(o.$,{type:"button",variant:"outline",asChild:!0,children:(0,a.jsx)(j(),{href:"/dashboard",children:"Cancel"})})]})]})})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>c});var a=s(60687);s(43210);var r=s(97822),n=s(78272),i=s(13964),o=s(3589),l=s(4780);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...i}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,l.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function p({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function x({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var a=s(60687);s(43210);var r=s(4780);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},44499:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,75758)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/profile/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/kaleidonex/src/app/profile/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48028:(e,t,s)=>{Promise.resolve().then(s.bind(s,9309))},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>l});var a=s(60687),r=s(43210),n=s(14163),i=r.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=s(4780);function l({className:e,...t}){return(0,a.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75758:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/profile/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89884:(e,t,s)=>{Promise.resolve().then(s.bind(s,75758))},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>o,TN:()=>l});var a=s(60687);s(43210);var r=s(24224),n=s(4780);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:t}),e),...s})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,415,658,818,489],()=>s(44499));module.exports=a})();