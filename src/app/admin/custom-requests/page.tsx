'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  FileText,
  Clock,
  DollarSign,
  User,
  Calendar,
  MessageSquare,
  CheckCircle,
  XCircle,
  PlayCircle,
  Loader2,
  AlertCircle,
  Filter,
  Phone,
  Mail,
  Eye
} from 'lucide-react';
import Link from 'next/link';
import { getCustomRequests, updateCustomRequestStatus, updateCustomRequestPaymentStatus, getAllUsers } from '@/lib/firebaseServices';
import { CustomRequest, User } from '@/types';
import StatusDropdown from '@/components/admin/StatusDropdown';

export default function AdminCustomRequestsPage() {
  const { user, userData } = useAuth();
  const [requests, setRequests] = useState<CustomRequest[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedRequest, setSelectedRequest] = useState<CustomRequest | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [updating, setUpdating] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    const fetchRequests = async () => {
      try {
        setLoading(true);
        const [fetchedRequests, fetchedUsers] = await Promise.all([
          getCustomRequests(),
          getAllUsers()
        ]);
        setRequests(fetchedRequests);
        setUsers(fetchedUsers);
      } catch (error: any) {
        console.error('Error fetching data:', error);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    if (user && userData?.role === 'admin') {
      fetchRequests();
    }
  }, [user, userData]);

  const handleUpdateStatus = async (requestId: string, status: CustomRequest['status']) => {
    try {
      setUpdating(true);
      await updateCustomRequestStatus(requestId, status, adminNotes);

      // Update local state
      setRequests(prev => prev.map(req =>
        req.id === requestId
          ? {
              ...req,
              status,
              adminNotes,
              updatedAt: new Date(),
              // Set payment status to pending when completing
              ...(status === 'completed' && { paymentStatus: 'pending' })
            }
          : req
      ));

      setSelectedRequest(null);
      setAdminNotes('');
    } catch (error: any) {
      console.error('Error updating request status:', error);
      setError('Failed to update request status');
    } finally {
      setUpdating(false);
    }
  };

  const handleUpdatePaymentStatus = async (requestId: string, paymentStatus: CustomRequest['paymentStatus']) => {
    try {
      setUpdating(true);
      await updateCustomRequestPaymentStatus(requestId, paymentStatus);

      // Update local state
      setRequests(prev => prev.map(req =>
        req.id === requestId
          ? { ...req, paymentStatus, updatedAt: new Date() }
          : req
      ));
    } catch (error: any) {
      console.error('Error updating payment status:', error);
      setError('Failed to update payment status');
    } finally {
      setUpdating(false);
    }
  };

  const handleShowUserDetails = (userEmail: string) => {
    const user = users.find(u => u.email === userEmail);
    if (user) {
      setSelectedUser(user);
    }
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  const getStatusIcon = (status: CustomRequest['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'in-progress':
        return <PlayCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: CustomRequest['status']) => {
    switch (status) {
      case 'pending':
        return 'outline';
      case 'in-progress':
        return 'secondary';
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Custom Requests Management
        </h1>
        <p className="text-gray-600">
          Manage and respond to custom design requests from users
        </p>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading custom requests...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats */}
      {!loading && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-xl font-bold">
                    {requests.filter(r => r.status === 'pending').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <PlayCircle className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">In Progress</p>
                  <p className="text-xl font-bold">
                    {requests.filter(r => r.status === 'in-progress').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-xl font-bold">
                    {requests.filter(r => r.status === 'completed').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total</p>
                  <p className="text-xl font-bold">{requests.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filter Section */}
      {!loading && (
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Custom Design Requests</h2>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Requests List */}
      {!loading && (
        <div className="space-y-4">
          {requests.filter(request => statusFilter === 'all' || request.status === statusFilter).length > 0 ? (
            requests.filter(request => statusFilter === 'all' || request.status === statusFilter).map((request) => (
              <Card key={request.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {request.title}
                        </h3>
                        <Badge variant={getStatusColor(request.status)} className="flex items-center space-x-1">
                          {getStatusIcon(request.status)}
                          <span>{request.status}</span>
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center space-x-2 text-sm">
                          <User className="h-4 w-4 text-gray-600" />
                          <button
                            onClick={() => handleShowUserDetails(request.userEmail)}
                            className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
                          >
                            {request.userEmail}
                          </button>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <FileText className="h-4 w-4" />
                          <span>{request.category}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(request.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                      
                      {request.budget && (
                        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-3">
                          <DollarSign className="h-4 w-4" />
                          <span>Budget: ${request.budget}</span>
                        </div>
                      )}
                      
                      <p className="text-gray-700 mb-4">{request.description}</p>
                      
                      {request.adminNotes && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                          <div className="flex items-center space-x-2 mb-2">
                            <MessageSquare className="h-4 w-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-800">Admin Notes</span>
                          </div>
                          <p className="text-sm text-blue-700">{request.adminNotes}</p>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <StatusDropdown
                        currentStatus={request.status}
                        onStatusChange={(status) => handleUpdateStatus(request.id, status)}
                        type="custom-request"
                        paymentStatus={request.paymentStatus}
                        onPaymentStatusChange={(paymentStatus) => handleUpdatePaymentStatus(request.id, paymentStatus)}
                      />
                      {(request.status === 'pending' || request.status === 'in-progress') && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedRequest(request)}
                        >
                          Add Notes
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Custom Requests</h3>
                <p className="text-gray-600">No custom design requests have been submitted yet.</p>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Request Management Modal */}
      {selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Manage Request: {selectedRequest.title}</CardTitle>
              <CardDescription>
                Update the status and add admin notes for this request
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="adminNotes">Admin Notes</Label>
                <Textarea
                  id="adminNotes"
                  placeholder="Add notes about this request..."
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  rows={4}
                />
              </div>
              
              <div className="flex space-x-2">
                {selectedRequest.status === 'pending' && (
                  <>
                    <Button 
                      onClick={() => handleUpdateStatus(selectedRequest.id, 'in-progress')}
                      disabled={updating}
                    >
                      {updating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Accept & Start
                    </Button>
                    <Button 
                      variant="destructive"
                      onClick={() => handleUpdateStatus(selectedRequest.id, 'cancelled')}
                      disabled={updating}
                    >
                      {updating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Decline
                    </Button>
                  </>
                )}
                
                {selectedRequest.status === 'in-progress' && (
                  <Button 
                    onClick={() => handleUpdateStatus(selectedRequest.id, 'completed')}
                    disabled={updating}
                  >
                    {updating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Mark Complete
                  </Button>
                )}
                
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSelectedRequest(null);
                    setAdminNotes('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* User Details Modal */}
      <Dialog open={!!selectedUser} onOpenChange={() => setSelectedUser(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Complete information about the user who submitted this request
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Email</p>
                    <p className="text-sm text-gray-600">{selectedUser.email}</p>
                  </div>
                </div>

                {selectedUser.fullName && (
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Full Name</p>
                      <p className="text-sm text-gray-600">{selectedUser.fullName}</p>
                    </div>
                  </div>
                )}

                {selectedUser.phoneNumber && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Phone Number</p>
                      <p className="text-sm text-gray-600">
                        {selectedUser.countryCode} {selectedUser.phoneNumber}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Member Since</p>
                    <p className="text-sm text-gray-600">
                      {new Date(selectedUser.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {selectedUser.purchasedTemplates && selectedUser.purchasedTemplates.length > 0 && (
                  <div className="flex items-start space-x-3">
                    <FileText className="h-4 w-4 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Purchased Templates</p>
                      <p className="text-sm text-gray-600">
                        {selectedUser.purchasedTemplates.length} template(s)
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
