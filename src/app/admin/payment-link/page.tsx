'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, CreditCard, Copy, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

export default function PaymentLinkPage() {
  const { user, userData } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    amount: '',
    description: '',
    customerEmail: '',
    customerName: ''
  });
  const [generatedLink, setGeneratedLink] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Simulate payment link generation
      const paymentLink = `https://pay.kaleidonex.com/checkout?amount=${formData.amount}&desc=${encodeURIComponent(formData.description)}&email=${formData.customerEmail}&name=${encodeURIComponent(formData.customerName)}`;
      setGeneratedLink(paymentLink);
      toast.success('Payment link generated successfully!');
    } catch (error) {
      console.error('Error generating payment link:', error);
      toast.error('Failed to generate payment link');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedLink);
    toast.success('Payment link copied to clipboard!');
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button asChild variant="outline" size="sm">
              <Link href="/admin">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Payment Link Generator
          </h1>
          <p className="text-gray-600">
            Generate secure payment links for your customers
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Payment Link Form */}
            <Card className="shadow-lg border-0">
              <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50 border-b">
                <CardTitle className="flex items-center text-xl">
                  <CreditCard className="mr-3 h-5 w-5 text-green-600" />
                  Create Payment Link
                </CardTitle>
                <CardDescription>
                  Fill in the details to generate a payment link
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="amount" className="text-sm font-medium">Amount (₹) *</Label>
                      <Input
                        id="amount"
                        type="number"
                        value={formData.amount}
                        onChange={(e) => handleInputChange('amount', e.target.value)}
                        placeholder="2499"
                        min="1"
                        className="h-11"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="customerEmail" className="text-sm font-medium">Customer Email *</Label>
                      <Input
                        id="customerEmail"
                        type="email"
                        value={formData.customerEmail}
                        onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                        placeholder="<EMAIL>"
                        className="h-11"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customerName" className="text-sm font-medium">Customer Name *</Label>
                    <Input
                      id="customerName"
                      value={formData.customerName}
                      onChange={(e) => handleInputChange('customerName', e.target.value)}
                      placeholder="John Doe"
                      className="h-11"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">Description *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Payment for Modern Dashboard Template"
                      rows={3}
                      className="resize-none"
                      required
                    />
                  </div>

                  <Button type="submit" disabled={loading} className="w-full" size="lg">
                    {loading ? 'Generating...' : 'Generate Payment Link'}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Generated Link Display */}
            <Card className="shadow-lg border-0">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b">
                <CardTitle className="flex items-center text-xl">
                  <ExternalLink className="mr-3 h-5 w-5 text-purple-600" />
                  Generated Payment Link
                </CardTitle>
                <CardDescription>
                  Share this link with your customer
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                {generatedLink ? (
                  <div className="space-y-4">
                    <div className="p-4 bg-gray-50 rounded-lg border">
                      <p className="text-sm text-gray-600 mb-2">Payment Link:</p>
                      <p className="text-sm font-mono break-all text-blue-600">{generatedLink}</p>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button onClick={copyToClipboard} variant="outline" className="flex-1">
                        <Copy className="mr-2 h-4 w-4" />
                        Copy Link
                      </Button>
                      <Button asChild className="flex-1">
                        <a href={generatedLink} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="mr-2 h-4 w-4" />
                          Open Link
                        </a>
                      </Button>
                    </div>

                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">Payment Details:</h4>
                      <div className="space-y-1 text-sm text-blue-800">
                        <p><strong>Amount:</strong> ₹{formData.amount}</p>
                        <p><strong>Customer:</strong> {formData.customerName}</p>
                        <p><strong>Email:</strong> {formData.customerEmail}</p>
                        <p><strong>Description:</strong> {formData.description}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CreditCard className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No payment link generated</h3>
                    <p className="text-gray-600">
                      Fill in the form and click "Generate Payment Link" to create a payment link.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <Card className="mt-8 shadow-lg border-0">
            <CardHeader>
              <CardTitle>How to Use Payment Links</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">1. Generate Link</h4>
                  <p>Fill in the customer details and amount to generate a secure payment link.</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">2. Share with Customer</h4>
                  <p>Send the generated link to your customer via email or messaging.</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">3. Customer Pays</h4>
                  <p>Customer clicks the link and completes the payment securely.</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">4. Receive Notification</h4>
                  <p>You'll receive a notification once the payment is completed.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
