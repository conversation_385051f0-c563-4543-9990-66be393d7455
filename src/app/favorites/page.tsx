'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Heart, 
  Star, 
  Eye,
  MessageCircle,
  Download,
  IndianRupee,
  Trash2
} from 'lucide-react';
import Link from 'next/link';
import { Template } from '@/types';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { createContactMessage } from '@/lib/firebaseServices';
import { toast } from 'sonner';

export default function FavoritesPage() {
  const { user, userData } = useAuth();
  const [favorites, setFavorites] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchFavorites = async () => {
      if (!user || !userData?.favoriteTemplates?.length) {
        setLoading(false);
        return;
      }

      try {
        // Fetch favorite templates from Firebase
        const templatesRef = collection(db, 'templates');
        const q = query(templatesRef, where('__name__', 'in', userData.favoriteTemplates));
        const querySnapshot = await getDocs(q);
        
        const favoriteTemplates = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Template[];

        setFavorites(favoriteTemplates);
      } catch (error: any) {
        console.error('Error fetching favorites:', error);
        setError('Failed to load your favorite templates');
      } finally {
        setLoading(false);
      }
    };

    fetchFavorites();
  }, [user, userData]);

  const handleContactRequest = async (template: Template) => {
    if (!user || !userData) {
      toast.error('Please sign in to contact us');
      return;
    }

    if (!userData.phoneNumber) {
      toast.error('Please complete your mobile number in profile settings before making contact requests');
      return;
    }

    try {
      await createContactMessage({
        userId: user.uid,
        userEmail: user.email!,
        userName: userData.fullName || user.email!.split('@')[0],
        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,
        subject: `Inquiry about ${template.title}`,
        message: `Hi, I'm interested in the ${template.title} template. Could you please provide more information?`,
        type: 'contact',
        templateId: template.id,
        templateTitle: template.title,
        status: 'pending'
      });

      toast.success('Contact request sent! We\'ll get back to you soon.');
    } catch (error) {
      console.error('Error sending contact request:', error);
      toast.error('Failed to send contact request. Please try again.');
    }
  };

  const handleBuyRequest = async (template: Template) => {
    if (!user || !userData) {
      toast.error('Please sign in to make a purchase request');
      return;
    }

    if (!userData.phoneNumber) {
      toast.error('Please complete your mobile number in profile settings before making purchase requests');
      return;
    }

    try {
      await createContactMessage({
        userId: user.uid,
        userEmail: user.email!,
        userName: userData.fullName || user.email!.split('@')[0],
        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,
        subject: `Purchase Request for ${template.title}`,
        message: `Hi, I would like to purchase the ${template.title} template. Please provide payment details and next steps.`,
        type: 'purchase-request',
        templateId: template.id,
        templateTitle: template.title,
        status: 'pending'
      });

      toast.success('Purchase request sent! We\'ll contact you with payment details soon.');
    } catch (error) {
      console.error('Error sending purchase request:', error);
      toast.error('Failed to send purchase request. Please try again.');
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Please sign in to view your favorites</h1>
        <Button asChild>
          <Link href="/auth">Sign In</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
          My Favorite Templates
        </h1>
        <p className="text-sm sm:text-base text-gray-600">
          Templates you've saved for later
        </p>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <div className="h-48 bg-gray-200 rounded-t-lg"></div>
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            <Heart className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Favorites</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      ) : favorites.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Heart className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Favorites Yet</h3>
          <p className="text-gray-600 mb-6">
            Start browsing templates and add them to your favorites
          </p>
          <Button asChild>
            <Link href="/templates">Browse Templates</Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {favorites.map((template) => (
            <Card key={template.id} className="group overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 shadow-lg cursor-pointer">
              {/* Image Container */}
              <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden">
                {template.imageUrl ? (
                  <img
                    src={template.imageUrl}
                    alt={template.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center space-y-2">
                      <div className="w-12 h-12 bg-gray-300 rounded-lg mx-auto"></div>
                      <p className="text-sm text-gray-500">No Preview</p>
                    </div>
                  </div>
                )}

                {/* Category Badge */}
                <div className="absolute top-2 sm:top-3 left-2 sm:left-3">
                  <Badge variant="secondary" className="bg-white/90 text-black border-0 text-xs">
                    {template.category}
                  </Badge>
                </div>

                {/* Remove from Favorites Button */}
                <div className="absolute top-2 sm:top-3 right-2 sm:right-3">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="w-6 h-6 sm:w-8 sm:h-8 p-0 bg-red-100 hover:bg-red-200 text-red-600 border-0 rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      // TODO: Implement remove from favorites
                      toast.info('Remove from favorites functionality coming soon!');
                    }}
                  >
                    <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                  </Button>
                </div>
              </div>

              {/* Card Content */}
              <CardHeader className="pb-3">
                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-base sm:text-xl font-bold group-hover:text-blue-600 transition-colors line-clamp-2">
                      {template.title}
                    </CardTitle>
                    <div className="flex items-center gap-1 text-yellow-500 flex-shrink-0 ml-2">
                      <Star className="h-3 w-3 sm:h-4 sm:w-4 fill-current" />
                      <span className="text-xs sm:text-sm font-medium">{template.rating || '4.9'}</span>
                    </div>
                  </div>
                  <CardDescription className="text-xs sm:text-sm leading-relaxed line-clamp-2">
                    {template.description}
                  </CardDescription>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Price */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-lg sm:text-2xl font-bold text-green-600 flex items-center">
                        <IndianRupee className="h-4 w-4 sm:h-5 sm:w-5" />
                        {template.minPrice && template.maxPrice ? 
                          `${template.minPrice}-${template.maxPrice}` : 
                          template.price === 0 ? 'Free' : template.price
                        }
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Button
                        className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 font-medium group/btn text-xs sm:text-sm cursor-pointer"
                        onClick={() => handleBuyRequest(template)}
                      >
                        <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform" />
                        <span className="hidden sm:inline">Contact to Buy</span>
                        <span className="sm:hidden">Buy</span>
                      </Button>
                    </div>

                    <Button
                      variant="outline"
                      className="w-full group/btn hover:bg-green-50 hover:border-green-200 hover:text-green-700 transition-all text-xs sm:text-sm cursor-pointer"
                      onClick={() => handleContactRequest(template)}
                    >
                      <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform" />
                      <span className="hidden sm:inline">Contact for Info</span>
                      <span className="sm:hidden">Contact</span>
                    </Button>
                  </div>

                  {/* Quick Info */}
                  <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                    <span className="flex items-center gap-1">
                      <Download className="h-3 w-3" />
                      <span className="hidden sm:inline">{(template.downloads || 1200).toLocaleString()} downloads</span>
                      <span className="sm:hidden">{(template.downloads || 1200) > 1000 ? `${Math.floor((template.downloads || 1200) / 1000)}k` : template.downloads}</span>
                    </span>
                    <span className="hidden sm:inline">Updated recently</span>
                    <span className="sm:hidden">Recent</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
